import { But<PERSON> } from "@/components/ui/button";
import { useAuth } from "@/hooks/use-auth";
import {
  BarChart3,
  Calendar,
  Frown,
  Home,
  Settings,
  Users,
  Activity
} from "lucide-react";
import { Link, useLocation } from "wouter";

export function Sidebar() {
  const { isAdmin } = useAuth();
  const [location] = useLocation();

  const adminNavItems = [
    { href: "/admin", icon: Home, label: "Dashboard" },
    { href: "/admin/recognition", icon: Frown, label: "Frown Recognition" },
    { href: "/attendance", icon: Calendar, label: "Attendance Logs" },
    { href: "/employees", icon: Users, label: "Employee Management" },
    { href: "/admin/reports", icon: BarChart3, label: "Reports & Analytics" },
    { href: "/admin/settings", icon: Settings, label: "Settings" },
    { href: "/system-test", icon: Activity, label: "System Test" },
  ];

  const studentNavItems = [
    { href: "/student", icon: Home, label: "Dashboard" },
    { href: "/student/checkin", icon: Frown, label: "Frown Recognition" },
    { href: "/attendance", icon: Calendar, label: "My Attendance" },
  ];

  const navItems = isAdmin ? adminNavItems : studentNavItems;

  return (
    <aside className="hidden lg:flex lg:flex-col lg:w-64 lg:bg-white lg:border-r lg:border-gray-200">
      <nav className="flex-1 px-4 py-6 space-y-2">
        {navItems.map((item) => {
          const isActive = location === item.href;
          return (
            <Link key={item.href} href={item.href}>
              <Button
                variant={isActive ? "default" : "ghost"}
                className="w-full justify-start"
              >
                <item.icon className="h-4 w-4 mr-3" />
                {item.label}
              </Button>
            </Link>
          );
        })}
      </nav>
    </aside>
  );
}
