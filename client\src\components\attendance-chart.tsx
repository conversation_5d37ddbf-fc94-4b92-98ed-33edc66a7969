import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useState } from "react";

export function AttendanceChart() {
  const [selectedPeriod, setSelectedPeriod] = useState("thisWeek");

  // Mock chart data - in production this would come from API
  const chartData = [
    { day: "Mon", value: 248, percentage: 85 },
    { day: "Tue", value: 261, percentage: 92 },
    { day: "Wed", value: 234, percentage: 78 },
    { day: "Thu", value: 251, percentage: 88 },
    { day: "Fri", value: 267, percentage: 95 },
    { day: "Sat", value: 0, percentage: 0, disabled: true },
    { day: "Sun", value: 0, percentage: 0, disabled: true },
  ];

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle className="text-lg font-medium">Weekly Attendance Trends</CardTitle>
        <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
          <SelectTrigger className="w-40">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="thisWeek">This Week</SelectItem>
            <SelectItem value="lastWeek">Last Week</SelectItem>
            <SelectItem value="thisMonth">This Month</SelectItem>
          </SelectContent>
        </Select>
      </CardHeader>
      
      <CardContent>
        <div className="grid grid-cols-7 gap-4 h-64">
          {chartData.map((item, index) => (
            <div key={index} className={`flex flex-col items-center justify-end space-y-2 ${item.disabled ? 'opacity-50' : ''}`}>
              <div
                className={`rounded-t-lg w-8 ${
                  item.disabled 
                    ? 'bg-gray-300' 
                    : item.day === 'Fri' 
                      ? 'bg-secondary' 
                      : 'bg-primary'
                }`}
                style={{ height: `${item.percentage}%` }}
              />
              <span className={`text-xs ${item.disabled ? 'text-gray-400' : 'text-gray-600'}`}>
                {item.day}
              </span>
              <span className={`text-xs font-medium ${
                item.disabled 
                  ? 'text-gray-400' 
                  : item.day === 'Fri' 
                    ? 'text-secondary' 
                    : 'text-gray-900'
              }`}>
                {item.disabled ? '--' : item.value}
              </span>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
