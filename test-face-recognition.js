// Test script for face recognition API
import http from 'http';

// Simple test image (1x1 pixel PNG in base64)
const testImageData = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAI9jU77zgAAAABJRU5ErkJggg==';

// Test face recognition endpoint
async function testFaceRecognition() {
  const data = JSON.stringify({
    imageData: testImageData
  });

  const options = {
    hostname: 'localhost',
    port: 5000,
    path: '/api/face-recognition',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Content-Length': data.length
    }
  };

  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let responseData = '';
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          data: responseData
        });
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.write(data);
    req.end();
  });
}

// Test image quality endpoint
async function testImageQuality() {
  const data = JSON.stringify({
    imageData: testImageData
  });

  const options = {
    hostname: 'localhost',
    port: 5000,
    path: '/api/image-quality',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Content-Length': data.length
    }
  };

  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let responseData = '';
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          data: responseData
        });
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.write(data);
    req.end();
  });
}

// Run tests
async function runTests() {
  console.log('🧪 Testing Face Recognition API...\n');

  try {
    console.log('1. Testing Face Recognition endpoint...');
    const faceResult = await testFaceRecognition();
    console.log(`Status: ${faceResult.statusCode}`);
    console.log(`Response: ${faceResult.data}\n`);

    console.log('2. Testing Image Quality endpoint...');
    const qualityResult = await testImageQuality();
    console.log(`Status: ${qualityResult.statusCode}`);
    console.log(`Response: ${qualityResult.data}\n`);

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

runTests();
