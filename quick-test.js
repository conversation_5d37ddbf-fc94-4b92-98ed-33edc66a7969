import http from 'http';
import fs from 'fs';

const imageBase64 = fs.readFileSync('test-real-face-small.jpg', 'base64');
const imageDataURL = `data:image/jpeg;base64,${imageBase64}`;

const data = JSON.stringify({ imageData: imageDataURL });

const options = {
  hostname: 'localhost',
  port: 5000,
  path: '/api/image-quality',
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Content-Length': data.length
  }
};

const req = http.request(options, (res) => {
  let responseData = '';
  res.on('data', (chunk) => {
    responseData += chunk;
  });
  res.on('end', () => {
    console.log('Response:', responseData);
  });
});

req.on('error', (error) => {
  console.error('Error:', error);
});

req.write(data);
req.end();
