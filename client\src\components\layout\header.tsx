import { But<PERSON> } from "@/components/ui/button";
import { useAuth } from "@/hooks/use-auth";
import { Camera, LogOut, Settings, User, Bell, Search, Loader2 } from "lucide-react";
import { Link, useLocation } from "wouter";

export function Header() {
  const { user, logout, isAdmin, isLoggingOut } = useAuth();
  const [location] = useLocation();

  return (
    <header className="bg-white/80 backdrop-blur-md shadow-lg border-b border-gray-200/50 sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          <div className="flex items-center space-x-4">
            <Link href="/" className="flex items-center space-x-3">
              <div className="bg-gradient-to-r from-primary to-blue-600 p-2 rounded-lg">
                <Camera className="h-5 w-5 text-white" />
              </div>
              <div>
                <h1 className="text-lg font-bold text-gray-900">AttendanceTracker Pro</h1>
                <p className="text-xs text-gray-500 hidden sm:block">Smart Recognition System</p>
              </div>
            </Link>
          </div>
          
          <div className="flex items-center space-x-4">
            <div className="hidden md:flex items-center space-x-2">
              {isAdmin && (
                <Link href="/admin">
                  <Button 
                    variant={location === "/admin" || location === "/" ? "default" : "ghost"} 
                    size="sm"
                    className="bg-gradient-to-r from-primary to-blue-600 hover:from-primary/90 hover:to-blue-700"
                  >
                    <Settings className="h-4 w-4 mr-2" />
                    Admin
                  </Button>
                </Link>
              )}
              <Link href="/student">
                <Button 
                  variant={location === "/student" ? "default" : "ghost"} 
                  size="sm"
                  className={location === "/student" ? "bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700" : ""}
                >
                  <User className="h-4 w-4 mr-2" />
                  Student
                </Button>
              </Link>
            </div>
            
            {/* Notification Bell */}
            <Button variant="ghost" size="sm" className="relative">
              <Bell className="h-4 w-4" />
              <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center">3</span>
            </Button>
            
            {/* User Profile */}
            <div className="flex items-center space-x-3 bg-gradient-to-r from-gray-50 to-gray-100 rounded-xl px-4 py-2 border">
              <div className="w-8 h-8 bg-gradient-to-r from-primary to-blue-600 rounded-full flex items-center justify-center">
                <User className="h-4 w-4 text-white" />
              </div>
              <div className="hidden sm:block text-left">
                <p className="text-sm font-semibold text-gray-900">{user?.fullName}</p>
                <p className="text-xs text-gray-600 capitalize">{user?.role}</p>
              </div>
            </div>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                console.log("🔓 Logout button clicked");
                logout();
              }}
              disabled={isLoggingOut}
              className="text-gray-600 hover:text-red-600 hover:bg-red-50 disabled:opacity-50"
            >
              {isLoggingOut ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <LogOut className="h-4 w-4" />
              )}
            </Button>
          </div>
        </div>
      </div>
    </header>
  );
}
