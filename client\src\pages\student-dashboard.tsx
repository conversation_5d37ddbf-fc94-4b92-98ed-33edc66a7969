import { Header } from "@/components/layout/header";
import { Sidebar } from "@/components/layout/sidebar";
import { FaceRecognitionPanel } from "@/components/face-recognition-panel";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { useAuth } from "@/hooks/use-auth";
import { useQuery } from "@tanstack/react-query";
import { format, isToday } from "date-fns";
import { Calendar, Clock, CheckCircle, XCircle, Camera, User, BarChart3, RefreshCw } from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";
import { useLocation } from "wouter";
import { useToast } from "@/hooks/use-toast";

interface AttendanceRecord {
  id: number;
  checkInTime: string;
  checkOutTime?: string;
  status: string;
  location: string;
}

export default function StudentDashboard() {
  const { user } = useAuth();
  const [, setLocation] = useLocation();
  const { toast } = useToast();

  const { data: attendanceRecords, isLoading } = useQuery<AttendanceRecord[]>({
    queryKey: ["/api/attendance/user", user?.id],
    enabled: !!user?.id,
  });

  const todayRecord = attendanceRecords?.find(record => 
    isToday(new Date(record.checkInTime))
  );

  const recentRecords = attendanceRecords?.slice(0, 10) || [];

  const handleFaceRecognition = () => {
    console.log("🔗 Navigating to Face Recognition");
    setLocation("/student/checkin");
  };

  const handleViewAttendance = () => {
    console.log("🔗 Navigating to Attendance History");
    setLocation("/attendance");
  };

  const handleRefreshData = () => {
    console.log("🔄 Refreshing attendance data");
    toast({
      title: "Refreshing Data",
      description: "Updating your attendance records...",
    });
    // This would trigger a refetch in a real app
    window.location.reload();
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "present":
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-100">Present</Badge>;
      case "late":
        return <Badge className="bg-amber-100 text-amber-800 hover:bg-amber-100">Late</Badge>;
      case "absent":
        return <Badge className="bg-red-100 text-red-800 hover:bg-red-100">Absent</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  return (
    <div className="min-h-screen flex flex-col bg-surface">
      <Header />
      
      <main className="flex-1 flex">
        <Sidebar />
        
        <div className="flex-1 p-6 lg:p-8">
          {/* Dashboard Header */}
          <div className="mb-8">
            <h2 className="text-2xl font-medium text-gray-900 mb-2">
              Welcome, {user?.fullName}
            </h2>
            <p className="text-gray-600">Track your attendance and manage your check-ins</p>
          </div>

          {/* Today's Status */}
          <div className="mb-8">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Calendar className="h-5 w-5 mr-2" />
                  Today's Status
                </CardTitle>
              </CardHeader>
              <CardContent>
                {todayRecord ? (
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <div className="flex items-center space-x-2">
                        <CheckCircle className="h-5 w-5 text-green-600" />
                        <span className="font-medium">Checked In</span>
                      </div>
                      <div className="text-sm text-gray-600">
                        <Clock className="h-4 w-4 inline mr-1" />
                        {format(new Date(todayRecord.checkInTime), "hh:mm a")}
                      </div>
                      <div>{getStatusBadge(todayRecord.status)}</div>
                    </div>
                    {todayRecord.checkOutTime && (
                      <div className="text-sm text-gray-600">
                        Checked Out: {format(new Date(todayRecord.checkOutTime), "hh:mm a")}
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="flex items-center space-x-2 text-gray-500">
                    <XCircle className="h-5 w-5" />
                    <span>Not checked in today</span>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Main Content Grid */}
          <div className="grid grid-cols-1 xl:grid-cols-3 gap-8">
            
            {/* Face Recognition Panel */}
            <div className="xl:col-span-1">
              <FaceRecognitionPanel />
            </div>

            {/* Recent Attendance History */}
            <div className="xl:col-span-2">
              <Card>
                <CardHeader>
                  <CardTitle>Recent Attendance</CardTitle>
                </CardHeader>
                <CardContent>
                  {isLoading ? (
                    <div className="space-y-4">
                      {[...Array(5)].map((_, i) => (
                        <Skeleton key={i} className="h-16 w-full" />
                      ))}
                    </div>
                  ) : (
                    <div className="overflow-x-auto">
                      <table className="w-full">
                        <thead>
                          <tr className="border-b border-gray-200">
                            <th className="text-left py-3 px-2 text-sm font-medium text-gray-600">Date</th>
                            <th className="text-left py-3 px-2 text-sm font-medium text-gray-600">Check In</th>
                            <th className="text-left py-3 px-2 text-sm font-medium text-gray-600">Check Out</th>
                            <th className="text-left py-3 px-2 text-sm font-medium text-gray-600">Status</th>
                            <th className="text-left py-3 px-2 text-sm font-medium text-gray-600">Location</th>
                          </tr>
                        </thead>
                        <tbody className="divide-y divide-gray-100">
                          {recentRecords.map((record) => (
                            <tr key={record.id} className="hover:bg-gray-50">
                              <td className="py-4 px-2 text-sm text-gray-900">
                                {format(new Date(record.checkInTime), "MMM dd, yyyy")}
                              </td>
                              <td className="py-4 px-2 text-sm text-gray-900">
                                {format(new Date(record.checkInTime), "hh:mm a")}
                              </td>
                              <td className="py-4 px-2 text-sm text-gray-900">
                                {record.checkOutTime 
                                  ? format(new Date(record.checkOutTime), "hh:mm a")
                                  : "--"
                                }
                              </td>
                              <td className="py-4 px-2">
                                {getStatusBadge(record.status)}
                              </td>
                              <td className="py-4 px-2 text-sm text-gray-600">
                                {record.location}
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                      
                      {recentRecords.length === 0 && (
                        <div className="text-center py-8 text-gray-500">
                          No attendance records found
                        </div>
                      )}
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="mt-8">
            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Button
                    onClick={handleFaceRecognition}
                    className="h-16 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 flex flex-col items-center justify-center space-y-1 transition-all duration-200 hover:scale-105"
                  >
                    <Camera className="h-6 w-6" />
                    <span className="text-sm">Face Recognition</span>
                  </Button>
                  <Button
                    onClick={handleViewAttendance}
                    className="h-16 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 flex flex-col items-center justify-center space-y-1 transition-all duration-200 hover:scale-105"
                  >
                    <BarChart3 className="h-6 w-6" />
                    <span className="text-sm">View Attendance</span>
                  </Button>
                  <Button
                    onClick={handleRefreshData}
                    variant="outline"
                    className="h-16 border-2 border-gray-200 hover:border-gray-300 flex flex-col items-center justify-center space-y-1 transition-all duration-200 hover:scale-105"
                  >
                    <RefreshCw className="h-6 w-6" />
                    <span className="text-sm">Refresh Data</span>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </main>
    </div>
  );
}
