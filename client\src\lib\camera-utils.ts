/**
 * Camera utility functions for browser compatibility and feature detection
 */

export interface BrowserSupport {
  hasMediaDevices: boolean;
  hasGetUserMedia: boolean;
  hasEnumerateDevices: boolean;
  hasPermissionsAPI: boolean;
  browserName: string;
  isSecureContext: boolean;
}

export interface CameraConstraints {
  video: MediaTrackConstraints;
  audio: boolean;
}

/**
 * Detect browser support for camera features
 */
export function detectBrowserSupport(): BrowserSupport {
  const hasMediaDevices = !!(navigator.mediaDevices);
  const hasGetUserMedia = !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia);
  const hasEnumerateDevices = !!(navigator.mediaDevices && navigator.mediaDevices.enumerateDevices);
  const hasPermissionsAPI = !!(navigator.permissions && navigator.permissions.query);
  const isSecureContext = window.isSecureContext || location.protocol === 'https:' || location.hostname === 'localhost';

  // Detect browser
  let browserName = 'Unknown';
  const userAgent = navigator.userAgent;
  
  if (userAgent.includes('Chrome')) {
    browserName = 'Chrome';
  } else if (userAgent.includes('Firefox')) {
    browserName = 'Firefox';
  } else if (userAgent.includes('Safari') && !userAgent.includes('Chrome')) {
    browserName = 'Safari';
  } else if (userAgent.includes('Edge')) {
    browserName = 'Edge';
  }

  return {
    hasMediaDevices,
    hasGetUserMedia,
    hasEnumerateDevices,
    hasPermissionsAPI,
    browserName,
    isSecureContext,
  };
}

/**
 * Get optimal camera constraints based on browser and device
 */
export function getOptimalConstraints(preferredWidth = 640, preferredHeight = 480): CameraConstraints {
  const support = detectBrowserSupport();
  
  const baseConstraints: MediaTrackConstraints = {
    width: { ideal: preferredWidth },
    height: { ideal: preferredHeight },
    facingMode: 'user', // Front-facing camera
  };

  // Browser-specific optimizations
  if (support.browserName === 'Safari') {
    // Safari sometimes has issues with ideal constraints
    return {
      video: {
        ...baseConstraints,
        width: { min: 320, ideal: preferredWidth, max: 1280 },
        height: { min: 240, ideal: preferredHeight, max: 720 },
      },
      audio: false,
    };
  }

  if (support.browserName === 'Firefox') {
    // Firefox works well with standard constraints
    return {
      video: {
        ...baseConstraints,
        frameRate: { ideal: 30 },
      },
      audio: false,
    };
  }

  // Chrome and Edge - use advanced constraints
  return {
    video: {
      ...baseConstraints,
      frameRate: { ideal: 30, max: 60 },
      aspectRatio: { ideal: 4/3 },
    },
    audio: false,
  };
}

/**
 * Get user-friendly error messages for camera errors
 */
export function getCameraErrorMessage(error: any): { type: string; message: string; suggestion: string } {
  const support = detectBrowserSupport();
  
  if (!support.isSecureContext) {
    return {
      type: 'security',
      message: 'Camera access requires a secure connection (HTTPS)',
      suggestion: 'Please access this page via HTTPS or localhost',
    };
  }

  if (!support.hasGetUserMedia) {
    return {
      type: 'not_supported',
      message: `Camera not supported in ${support.browserName}`,
      suggestion: 'Please use a modern browser like Chrome, Firefox, Safari, or Edge',
    };
  }

  // Handle specific error types
  switch (error.name) {
    case 'NotAllowedError':
    case 'PermissionDeniedError':
      return {
        type: 'permission',
        message: 'Camera access was denied',
        suggestion: 'Click the camera icon in your browser\'s address bar and allow camera access',
      };

    case 'NotFoundError':
    case 'DevicesNotFoundError':
      return {
        type: 'not_found',
        message: 'No camera device found',
        suggestion: 'Please connect a camera and refresh the page',
      };

    case 'NotReadableError':
    case 'TrackStartError':
      return {
        type: 'hardware',
        message: 'Camera is already in use by another application',
        suggestion: 'Close other applications using the camera and try again',
      };

    case 'OverconstrainedError':
    case 'ConstraintNotSatisfiedError':
      return {
        type: 'constraints',
        message: 'Camera does not support the required settings',
        suggestion: 'Try using a different camera or refresh the page',
      };

    case 'NotSupportedError':
      return {
        type: 'not_supported',
        message: 'Camera feature not supported',
        suggestion: 'Please update your browser to the latest version',
      };

    case 'AbortError':
      return {
        type: 'aborted',
        message: 'Camera access was interrupted',
        suggestion: 'Please try again',
      };

    default:
      return {
        type: 'unknown',
        message: error.message || 'An unknown camera error occurred',
        suggestion: 'Please refresh the page and try again',
      };
  }
}

/**
 * Check if the current environment supports camera access
 */
export function canAccessCamera(): { supported: boolean; reason?: string } {
  const support = detectBrowserSupport();

  if (!support.isSecureContext) {
    return {
      supported: false,
      reason: 'Camera access requires HTTPS or localhost',
    };
  }

  if (!support.hasGetUserMedia) {
    return {
      supported: false,
      reason: 'Browser does not support camera access',
    };
  }

  return { supported: true };
}

/**
 * Convert image data URL to blob for uploading
 */
export function dataURLToBlob(dataURL: string): Blob {
  const arr = dataURL.split(',');
  const mime = arr[0].match(/:(.*?);/)?.[1] || 'image/jpeg';
  const bstr = atob(arr[1]);
  let n = bstr.length;
  const u8arr = new Uint8Array(n);
  
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n);
  }
  
  return new Blob([u8arr], { type: mime });
}

/**
 * Compress image data URL to reduce file size
 */
export function compressImage(dataURL: string, quality = 0.8, maxWidth = 800): Promise<string> {
  return new Promise((resolve) => {
    const img = new Image();
    img.onload = () => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d')!;
      
      // Calculate new dimensions
      const ratio = Math.min(maxWidth / img.width, maxWidth / img.height);
      canvas.width = img.width * ratio;
      canvas.height = img.height * ratio;
      
      // Draw and compress
      ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
      const compressedDataURL = canvas.toDataURL('image/jpeg', quality);
      resolve(compressedDataURL);
    };
    img.src = dataURL;
  });
}
