import { Head<PERSON> } from "@/components/layout/header";
import { Sidebar } from "@/components/layout/sidebar";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useQuery } from "@tanstack/react-query";
import { AttendanceChart } from "@/components/attendance-chart";
import { 
  BarChart3, 
  Download, 
  Calendar, 
  Users, 
  TrendingUp, 
  TrendingDown,
  FileText,
  PieChart,
  Activity
} from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";

interface AttendanceStats {
  present: number;
  absent: number;
  late: number;
  total: number;
}

export default function Reports() {
  const { data: stats, isLoading } = useQuery<AttendanceStats>({
    queryKey: ["/api/attendance/stats"],
  });

  const { data: recentAttendance } = useQuery<any[]>({
    queryKey: ["/api/attendance/recent"],
  });

  const generateReport = (type: string) => {
    // Mock report generation
    const reportData = {
      daily: "Daily Attendance Report - " + new Date().toLocaleDateString(),
      weekly: "Weekly Attendance Summary - Week of " + new Date().toLocaleDateString(),
      monthly: "Monthly Analytics Report - " + new Date().toLocaleDateString('en-US', { month: 'long', year: 'numeric' }),
      custom: "Custom Date Range Report"
    };
    
    // In a real app, this would trigger a download
    alert(`Generating ${reportData[type as keyof typeof reportData]}...`);
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex flex-col bg-gradient-to-br from-slate-50 to-blue-50">
        <Header />
        <main className="flex-1 flex">
          <Sidebar />
          <div className="flex-1 p-6 lg:p-8">
            <Skeleton className="h-32 w-full mb-8" />
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {[...Array(6)].map((_, i) => (
                <Skeleton key={i} className="h-48 w-full" />
              ))}
            </div>
          </div>
        </main>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-br from-slate-50 to-blue-50">
      <Header />
      
      <main className="flex-1 flex">
        <Sidebar />
        
        <div className="flex-1 p-6 lg:p-8">
          {/* Page Header */}
          <div className="mb-8">
            <div className="bg-white/60 backdrop-blur-sm rounded-2xl p-6 border border-white/20 shadow-lg">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="bg-gradient-to-r from-green-500 to-emerald-600 p-3 rounded-xl">
                    <BarChart3 className="h-8 w-8 text-white" />
                  </div>
                  <div>
                    <h2 className="text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent">
                      Reports & Analytics
                    </h2>
                    <p className="text-gray-600 text-lg">Comprehensive attendance insights and data export</p>
                  </div>
                </div>
                <Button 
                  className="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700"
                  onClick={() => generateReport('daily')}
                >
                  <Download className="h-4 w-4 mr-2" />
                  Export Report
                </Button>
              </div>
              <div className="mt-4 flex items-center space-x-4 text-sm text-gray-500">
                <span className="flex items-center">
                  <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                  Real-time Data
                </span>
                <span>|</span>
                <span>{new Date().toLocaleDateString('en-US', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' })}</span>
              </div>
            </div>
          </div>

          {/* Quick Stats */}
          {stats && (
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
              <Card className="bg-white/60 backdrop-blur-sm border border-white/20 shadow-lg">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Present Today</p>
                      <p className="text-2xl font-bold text-green-600">{stats.present}</p>
                    </div>
                    <div className="bg-green-100 rounded-lg p-3">
                      <Users className="h-6 w-6 text-green-600" />
                    </div>
                  </div>
                  <div className="mt-4 flex items-center text-sm">
                    <TrendingUp className="h-4 w-4 mr-1 text-green-600" />
                    <span className="font-medium text-green-600">+12%</span>
                    <span className="text-gray-500 ml-1">vs yesterday</span>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-white/60 backdrop-blur-sm border border-white/20 shadow-lg">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Absent Today</p>
                      <p className="text-2xl font-bold text-red-600">{stats.absent}</p>
                    </div>
                    <div className="bg-red-100 rounded-lg p-3">
                      <Users className="h-6 w-6 text-red-600" />
                    </div>
                  </div>
                  <div className="mt-4 flex items-center text-sm">
                    <TrendingDown className="h-4 w-4 mr-1 text-red-600" />
                    <span className="font-medium text-red-600">-5%</span>
                    <span className="text-gray-500 ml-1">vs yesterday</span>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-white/60 backdrop-blur-sm border border-white/20 shadow-lg">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Late Arrivals</p>
                      <p className="text-2xl font-bold text-amber-600">{stats.late}</p>
                    </div>
                    <div className="bg-amber-100 rounded-lg p-3">
                      <Activity className="h-6 w-6 text-amber-600" />
                    </div>
                  </div>
                  <div className="mt-4 flex items-center text-sm">
                    <TrendingUp className="h-4 w-4 mr-1 text-amber-600" />
                    <span className="font-medium text-amber-600">+3</span>
                    <span className="text-gray-500 ml-1">vs yesterday</span>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-white/60 backdrop-blur-sm border border-white/20 shadow-lg">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Attendance Rate</p>
                      <p className="text-2xl font-bold text-blue-600">
                        {stats.total > 0 ? Math.round(((stats.present + stats.late) / stats.total) * 100) : 0}%
                      </p>
                    </div>
                    <div className="bg-blue-100 rounded-lg p-3">
                      <PieChart className="h-6 w-6 text-blue-600" />
                    </div>
                  </div>
                  <div className="mt-4 flex items-center text-sm">
                    <TrendingUp className="h-4 w-4 mr-1 text-blue-600" />
                    <span className="font-medium text-blue-600">+8%</span>
                    <span className="text-gray-500 ml-1">this month</span>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {/* Report Generation */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <Card className="bg-white/60 backdrop-blur-sm border border-white/20 shadow-lg">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <FileText className="h-5 w-5 mr-2 text-blue-600" />
                  Generate Reports
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 gap-3">
                  <Button 
                    variant="outline" 
                    className="justify-start h-12"
                    onClick={() => generateReport('daily')}
                  >
                    <Calendar className="h-4 w-4 mr-3" />
                    <div className="text-left">
                      <p className="font-medium">Daily Report</p>
                      <p className="text-xs text-gray-500">Today's attendance summary</p>
                    </div>
                  </Button>
                  
                  <Button 
                    variant="outline" 
                    className="justify-start h-12"
                    onClick={() => generateReport('weekly')}
                  >
                    <BarChart3 className="h-4 w-4 mr-3" />
                    <div className="text-left">
                      <p className="font-medium">Weekly Report</p>
                      <p className="text-xs text-gray-500">7-day attendance analysis</p>
                    </div>
                  </Button>
                  
                  <Button 
                    variant="outline" 
                    className="justify-start h-12"
                    onClick={() => generateReport('monthly')}
                  >
                    <PieChart className="h-4 w-4 mr-3" />
                    <div className="text-left">
                      <p className="font-medium">Monthly Report</p>
                      <p className="text-xs text-gray-500">Complete monthly analytics</p>
                    </div>
                  </Button>
                  
                  <Button 
                    variant="outline" 
                    className="justify-start h-12"
                    onClick={() => generateReport('custom')}
                  >
                    <Activity className="h-4 w-4 mr-3" />
                    <div className="text-left">
                      <p className="font-medium">Custom Range</p>
                      <p className="text-xs text-gray-500">Select specific date range</p>
                    </div>
                  </Button>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-white/60 backdrop-blur-sm border border-white/20 shadow-lg">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Download className="h-5 w-5 mr-2 text-green-600" />
                  Export Options
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <FileText className="h-5 w-5 text-blue-600" />
                      <div>
                        <p className="font-medium text-sm">PDF Report</p>
                        <p className="text-xs text-gray-500">Formatted for printing</p>
                      </div>
                    </div>
                    <Badge variant="secondary">Recommended</Badge>
                  </div>
                  
                  <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <BarChart3 className="h-5 w-5 text-green-600" />
                      <div>
                        <p className="font-medium text-sm">Excel Spreadsheet</p>
                        <p className="text-xs text-gray-500">Raw data for analysis</p>
                      </div>
                    </div>
                    <Badge variant="outline">CSV</Badge>
                  </div>
                  
                  <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <PieChart className="h-5 w-5 text-purple-600" />
                      <div>
                        <p className="font-medium text-sm">Visual Charts</p>
                        <p className="text-xs text-gray-500">Graphs and visualizations</p>
                      </div>
                    </div>
                    <Badge variant="outline">PNG</Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Attendance Chart */}
          <div className="mb-8">
            <AttendanceChart />
          </div>

          {/* Recent Activity Summary */}
          <Card className="bg-white/60 backdrop-blur-sm border border-white/20 shadow-lg">
            <CardHeader>
              <CardTitle className="flex items-center">
                <Activity className="h-5 w-5 mr-2 text-blue-600" />
                Recent Activity Summary
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="text-center p-4 bg-green-50 rounded-lg">
                    <p className="text-2xl font-bold text-green-600">{recentAttendance?.length || 0}</p>
                    <p className="text-sm text-green-700">Recent Check-ins</p>
                  </div>
                  <div className="text-center p-4 bg-blue-50 rounded-lg">
                    <p className="text-2xl font-bold text-blue-600">95%</p>
                    <p className="text-sm text-blue-700">Average Accuracy</p>
                  </div>
                  <div className="text-center p-4 bg-purple-50 rounded-lg">
                    <p className="text-2xl font-bold text-purple-600">2.3s</p>
                    <p className="text-sm text-purple-700">Avg Recognition Time</p>
                  </div>
                </div>
                
                <div className="pt-4 border-t border-gray-200">
                  <p className="text-sm text-gray-600">
                    Last updated: {new Date().toLocaleString()}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  );
}
