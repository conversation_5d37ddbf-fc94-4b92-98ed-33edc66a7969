const fs = require('fs');
const path = require('path');
const https = require('https');

const MODEL_BASE_URL = 'https://raw.githubusercontent.com/justadudewhohacks/face-api.js/master/weights';

const MODELS = [
  // Face detection models
  {
    name: 'ssd_mobilenetv1_model-weights_manifest.json',
    url: `${MODEL_BASE_URL}/ssd_mobilenetv1_model-weights_manifest.json`
  },
  {
    name: 'ssd_mobilenetv1_model-shard1.bin',
    url: `${MODEL_BASE_URL}/ssd_mobilenetv1_model-shard1.bin`
  },
  
  // Face landmark models
  {
    name: 'face_landmark_68_model-weights_manifest.json',
    url: `${MODEL_BASE_URL}/face_landmark_68_model-weights_manifest.json`
  },
  {
    name: 'face_landmark_68_model-shard1.bin',
    url: `${MODEL_BASE_URL}/face_landmark_68_model-shard1.bin`
  },
  
  // Face recognition models
  {
    name: 'face_recognition_model-weights_manifest.json',
    url: `${MODEL_BASE_URL}/face_recognition_model-weights_manifest.json`
  },
  {
    name: 'face_recognition_model-shard1.bin',
    url: `${MODEL_BASE_URL}/face_recognition_model-shard1.bin`
  },
  {
    name: 'face_recognition_model-shard2.bin',
    url: `${MODEL_BASE_URL}/face_recognition_model-shard2.bin`
  },
  
  // Face expression models
  {
    name: 'face_expression_model-weights_manifest.json',
    url: `${MODEL_BASE_URL}/face_expression_model-weights_manifest.json`
  },
  {
    name: 'face_expression_model-shard1.bin',
    url: `${MODEL_BASE_URL}/face_expression_model-shard1.bin`
  }
];

function downloadFile(url, destination) {
  return new Promise((resolve, reject) => {
    console.log(`📥 Downloading ${path.basename(destination)}...`);
    
    const file = fs.createWriteStream(destination);
    
    https.get(url, (response) => {
      if (response.statusCode !== 200) {
        reject(new Error(`Failed to download ${url}: ${response.statusCode}`));
        return;
      }
      
      response.pipe(file);
      
      file.on('finish', () => {
        file.close();
        console.log(`✅ Downloaded ${path.basename(destination)}`);
        resolve();
      });
      
      file.on('error', (err) => {
        fs.unlink(destination, () => {}); // Delete the file on error
        reject(err);
      });
    }).on('error', (err) => {
      reject(err);
    });
  });
}

async function downloadModels() {
  try {
    console.log('🤖 Starting Face Recognition Models Download...');
    
    // Create models directory
    const modelsDir = path.join(__dirname, '..', 'models');
    if (!fs.existsSync(modelsDir)) {
      fs.mkdirSync(modelsDir, { recursive: true });
      console.log(`📁 Created models directory: ${modelsDir}`);
    }
    
    // Download all models
    for (const model of MODELS) {
      const destination = path.join(modelsDir, model.name);
      
      // Skip if file already exists
      if (fs.existsSync(destination)) {
        console.log(`⏭️  Skipping ${model.name} (already exists)`);
        continue;
      }
      
      try {
        await downloadFile(model.url, destination);
      } catch (error) {
        console.error(`❌ Failed to download ${model.name}:`, error.message);
        throw error;
      }
    }
    
    console.log('🎉 All models downloaded successfully!');
    console.log(`📂 Models location: ${modelsDir}`);
    
    // Verify all files exist
    const missingFiles = MODELS.filter(model => {
      const filePath = path.join(modelsDir, model.name);
      return !fs.existsSync(filePath);
    });
    
    if (missingFiles.length > 0) {
      console.error('❌ Missing model files:', missingFiles.map(f => f.name));
      process.exit(1);
    }
    
    console.log('✅ All model files verified successfully!');
    
  } catch (error) {
    console.error('❌ Model download failed:', error);
    process.exit(1);
  }
}

// Run the download
downloadModels();
