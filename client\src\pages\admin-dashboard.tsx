import { Head<PERSON> } from "@/components/layout/header";
import { Sidebar } from "@/components/layout/sidebar";
import { StatsCards } from "@/components/stats-cards";
import { FaceRecognitionPanel } from "@/components/face-recognition-panel";
import { RecentActivity } from "@/components/recent-activity";
import { AttendanceChart } from "@/components/attendance-chart";
import { Button } from "@/components/ui/button";
import { Camera, Calendar, BarChart3, Users, Download } from "lucide-react";
import { useLocation } from "wouter";
import { useToast } from "@/hooks/use-toast";

export default function AdminDashboard() {
  const [, setLocation] = useLocation();
  const { toast } = useToast();

  const handleManageEmployees = () => {
    console.log("🔗 Navigating to Employee Management");
    setLocation("/employees");
  };

  const handleViewReports = () => {
    console.log("🔗 Navigating to Reports");
    setLocation("/admin/reports");
  };

  const handleExportData = async () => {
    console.log("📊 Exporting attendance data");
    try {
      // Simulate data export
      toast({
        title: "Export Started",
        description: "Attendance data is being exported...",
      });

      // In a real app, this would call an export API
      setTimeout(() => {
        toast({
          title: "Export Complete",
          description: "Attendance data has been exported successfully.",
        });
      }, 2000);
    } catch (error) {
      toast({
        title: "Export Failed",
        description: "Failed to export attendance data.",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-br from-slate-50 to-blue-50">
      <Header />
      
      <main className="flex-1 flex">
        <Sidebar />
        
        <div className="flex-1 p-6 lg:p-8">
          {/* Dashboard Header */}
          <div className="mb-8">
            <div className="bg-white/60 backdrop-blur-sm rounded-2xl p-6 border border-white/20 shadow-lg">
              <h2 className="text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent mb-3">
                Attendance Dashboard
              </h2>
              <p className="text-gray-600 text-lg">Monitor real-time attendance and manage employee check-ins</p>
              <div className="mt-4 flex items-center space-x-4 text-sm text-gray-500">
                <span className="flex items-center">
                  <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                  System Online
                </span>
                <span>|</span>
                <span>{new Date().toLocaleDateString('en-US', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' })}</span>
              </div>
            </div>
          </div>

          {/* Stats Cards */}
          <div className="mb-8">
            <StatsCards />
          </div>

          {/* Main Content Grid */}
          <div className="grid grid-cols-1 xl:grid-cols-3 gap-8">
            
            {/* Face Recognition Panel */}
            <div className="xl:col-span-1">
              <FaceRecognitionPanel />
            </div>

            {/* Recent Activity */}
            <div className="xl:col-span-2">
              <RecentActivity />
            </div>
          </div>

          {/* Attendance Chart */}
          <div className="mt-8">
            <AttendanceChart />
          </div>

          {/* Quick Actions */}
          <div className="mt-8">
            <div className="bg-white/60 backdrop-blur-sm rounded-2xl p-6 border border-white/20 shadow-lg">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Button
                  onClick={handleManageEmployees}
                  className="h-16 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 flex flex-col items-center justify-center space-y-1 transition-all duration-200 hover:scale-105"
                >
                  <Users className="h-6 w-6" />
                  <span className="text-sm">Manage Employees</span>
                </Button>
                <Button
                  onClick={handleViewReports}
                  className="h-16 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 flex flex-col items-center justify-center space-y-1 transition-all duration-200 hover:scale-105"
                >
                  <BarChart3 className="h-6 w-6" />
                  <span className="text-sm">View Reports</span>
                </Button>
                <Button
                  onClick={handleExportData}
                  className="h-16 bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 flex flex-col items-center justify-center space-y-1 transition-all duration-200 hover:scale-105"
                >
                  <Download className="h-6 w-6" />
                  <span className="text-sm">Export Data</span>
                </Button>
              </div>
            </div>
          </div>
        </div>
      </main>

      {/* Mobile Navigation */}
      <div className="lg:hidden fixed bottom-0 left-0 right-0 bg-white/80 backdrop-blur-md border-t border-gray-200/50 px-4 py-2 shadow-lg">
        <div className="flex justify-around">
          <Button
            variant="default"
            size="sm"
            onClick={() => setLocation("/admin")}
            className="flex flex-col items-center py-2 px-3 bg-gradient-to-r from-primary to-blue-600"
          >
            <BarChart3 className="h-4 w-4" />
            <span className="text-xs mt-1">Dashboard</span>
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setLocation("/admin/recognition")}
            className="flex flex-col items-center py-2 px-3"
          >
            <Camera className="h-4 w-4" />
            <span className="text-xs mt-1">Check In</span>
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setLocation("/attendance")}
            className="flex flex-col items-center py-2 px-3"
          >
            <Calendar className="h-4 w-4" />
            <span className="text-xs mt-1">History</span>
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setLocation("/employees")}
            className="flex flex-col items-center py-2 px-3"
          >
            <Users className="h-4 w-4" />
            <span className="text-xs mt-1">Employees</span>
          </Button>
        </div>
      </div>
    </div>
  );
}
