import { users, attendanceRecords, type User, type InsertUser, type AttendanceRecord, type InsertAttendance } from "@shared/schema";

export interface IStorage {
  // User management
  getUser(id: number): Promise<User | undefined>;
  getUserByUsername(username: string): Promise<User | undefined>;
  getUserByEmployeeId(employeeId: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;
  updateUser(id: number, updates: Partial<User>): Promise<User | undefined>;
  getAllUsers(): Promise<User[]>;
  
  // Attendance management
  createAttendanceRecord(record: InsertAttendance): Promise<AttendanceRecord>;
  getAttendanceByUserId(userId: number): Promise<AttendanceRecord[]>;
  getAttendanceByDate(date: string): Promise<AttendanceRecord[]>;
  getAllAttendanceRecords(): Promise<AttendanceRecord[]>;
  updateAttendanceRecord(id: number, updates: Partial<AttendanceRecord>): Promise<AttendanceRecord | undefined>;
  
  // Statistics
  getTodayAttendanceStats(): Promise<{
    present: number;
    absent: number;
    late: number;
    total: number;
  }>;
}

export class MemStorage implements IStorage {
  private users: Map<number, User>;
  private attendanceRecords: Map<number, AttendanceRecord>;
  private currentUserId: number;
  private currentAttendanceId: number;

  constructor() {
    this.users = new Map();
    this.attendanceRecords = new Map();
    this.currentUserId = 1;
    this.currentAttendanceId = 1;
    
    // Initialize with sample data
    this.initializeSampleData();
  }

  private initializeSampleData() {
    // Create admin user
    const admin: User = {
      id: this.currentUserId++,
      username: "admin",
      password: "admin123", // In production, this should be hashed
      fullName: "Sarah Johnson",
      role: "admin",
      department: "Administration",
      employeeId: "ADM001",
      profileImage: null,
      faceDescriptor: null,
      faceEnrolled: false,
      enrollmentDate: null,
      isActive: true,
    };
    this.users.set(admin.id, admin);

    // Create sample students/employees
    const sampleUsers = [
      {
        username: "michael.chen",
        password: "password123",
        fullName: "Michael Chen",
        role: "student",
        department: "Engineering",
        employeeId: "EMP001",
      },
      {
        username: "sarah.wilson",
        password: "password123",
        fullName: "Sarah Wilson",
        role: "student",
        department: "Marketing",
        employeeId: "EMP002",
      },
      {
        username: "david.rodriguez",
        password: "password123",
        fullName: "David Rodriguez",
        role: "student",
        department: "Engineering",
        employeeId: "EMP003",
      },
    ];

    sampleUsers.forEach(userData => {
      const user: User = {
        id: this.currentUserId++,
        ...userData,
        profileImage: null,
        faceDescriptor: null,
        faceEnrolled: false,
        enrollmentDate: null,
        isActive: true,
      };
      this.users.set(user.id, user);
    });

    // Create sample attendance records for today
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    const attendanceData = [
      { userId: 2, time: "08:47", status: "present", location: "Main Office" },
      { userId: 3, time: "08:52", status: "present", location: "Remote" },
      { userId: 4, time: "09:15", status: "late", location: "Main Office" },
    ];

    attendanceData.forEach(data => {
      const checkInTime = new Date(today);
      const [hours, minutes] = data.time.split(":");
      checkInTime.setHours(parseInt(hours), parseInt(minutes));

      const record: AttendanceRecord = {
        id: this.currentAttendanceId++,
        userId: data.userId,
        checkInTime,
        checkOutTime: null,
        status: data.status,
        location: data.location,
        recognitionConfidence: Math.floor(Math.random() * 20) + 80, // 80-100%
      };
      this.attendanceRecords.set(record.id, record);
    });
  }

  async getUser(id: number): Promise<User | undefined> {
    return this.users.get(id);
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    return Array.from(this.users.values()).find(
      (user) => user.username === username,
    );
  }

  async getUserByEmployeeId(employeeId: string): Promise<User | undefined> {
    return Array.from(this.users.values()).find(
      (user) => user.employeeId === employeeId,
    );
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const id = this.currentUserId++;
    const user: User = {
      ...insertUser,
      id,
      isActive: true,
      department: insertUser.department ?? null,
      employeeId: insertUser.employeeId ?? null,
      profileImage: insertUser.profileImage ?? null,
      faceDescriptor: null,
      faceEnrolled: false,
      enrollmentDate: null,
    };
    this.users.set(id, user);
    return user;
  }

  async updateUser(id: number, updates: Partial<User>): Promise<User | undefined> {
    const user = this.users.get(id);
    if (!user) return undefined;
    
    const updatedUser = { ...user, ...updates };
    this.users.set(id, updatedUser);
    return updatedUser;
  }

  async getAllUsers(): Promise<User[]> {
    return Array.from(this.users.values());
  }

  async createAttendanceRecord(record: InsertAttendance): Promise<AttendanceRecord> {
    const id = this.currentAttendanceId++;
    const attendanceRecord: AttendanceRecord = {
      ...record,
      id,
      checkOutTime: record.checkOutTime ?? null,
      recognitionConfidence: record.recognitionConfidence ?? null,
    };
    this.attendanceRecords.set(id, attendanceRecord);
    return attendanceRecord;
  }

  async getAttendanceByUserId(userId: number): Promise<AttendanceRecord[]> {
    return Array.from(this.attendanceRecords.values()).filter(
      (record) => record.userId === userId,
    );
  }

  async getAttendanceByDate(date: string): Promise<AttendanceRecord[]> {
    const targetDate = new Date(date);
    targetDate.setHours(0, 0, 0, 0);
    const nextDay = new Date(targetDate);
    nextDay.setDate(nextDay.getDate() + 1);

    return Array.from(this.attendanceRecords.values()).filter((record) => {
      const recordDate = new Date(record.checkInTime);
      return recordDate >= targetDate && recordDate < nextDay;
    });
  }

  async getAllAttendanceRecords(): Promise<AttendanceRecord[]> {
    return Array.from(this.attendanceRecords.values());
  }

  async updateAttendanceRecord(id: number, updates: Partial<AttendanceRecord>): Promise<AttendanceRecord | undefined> {
    const record = this.attendanceRecords.get(id);
    if (!record) return undefined;
    
    const updatedRecord = { ...record, ...updates };
    this.attendanceRecords.set(id, updatedRecord);
    return updatedRecord;
  }

  async getTodayAttendanceStats(): Promise<{
    present: number;
    absent: number;
    late: number;
    total: number;
  }> {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const todayRecords = await this.getAttendanceByDate(today.toISOString());

    // Get all users (both students and admin)
    const allUsers = Array.from(this.users.values());
    const totalUsers = allUsers.length;

    // Get unique users who have checked in today (latest record per user)
    const userAttendanceMap = new Map<number, AttendanceRecord>();
    todayRecords.forEach(record => {
      const existing = userAttendanceMap.get(record.userId);
      if (!existing || new Date(record.checkInTime) > new Date(existing.checkInTime)) {
        userAttendanceMap.set(record.userId, record);
      }
    });

    // Count only users who are currently checked in (no checkout time)
    const activeRecords = Array.from(userAttendanceMap.values()).filter(r => !r.checkOutTime);
    const present = activeRecords.filter(r => r.status === 'present').length;
    const late = activeRecords.filter(r => r.status === 'late').length;
    const checkedIn = present + late;
    const absent = Math.max(0, totalUsers - checkedIn);

    return {
      present: present,
      absent: absent,
      late: late,
      total: totalUsers,
    };
  }
}

export const storage = new MemStorage();
