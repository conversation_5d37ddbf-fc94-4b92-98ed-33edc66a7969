import { Switch, Route, useLocation } from "wouter";
import { queryClient } from "./lib/queryClient";
import { QueryClientProvider } from "@tanstack/react-query";
import { Toaster } from "@/components/ui/toaster";
import { TooltipProvider } from "@/components/ui/tooltip";
import { useAuth } from "@/hooks/use-auth";
import { useEffect } from "react";
import Login from "@/pages/login";
import AdminDashboard from "@/pages/admin-dashboard";
import StudentDashboard from "@/pages/student-dashboard";
import EmployeeManagement from "@/pages/employee-management";
import AttendanceLogs from "@/pages/attendance-logs";
import FrownRecognition from "@/pages/frown-recognition";
import Reports from "@/pages/reports";
import Settings from "@/pages/settings";
import SystemTest from "@/pages/system-test";
import LogoutTest from "@/pages/logout-test";
import QuickActionsTest from "@/pages/quick-actions-test";
import { CameraTest } from "@/components/camera-test";
import NotFound from "@/pages/not-found";

function ProtectedRoute({ component: Component, requireAdmin = false }: { component: React.ComponentType; requireAdmin?: boolean }) {
  const { isAuthenticated, isAdmin, isLoading } = useAuth();

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return <Login />;
  }

  if (requireAdmin && !isAdmin) {
    return <NotFound />;
  }

  return <Component />;
}

function Router() {
  const { isAuthenticated, isAdmin, isLoading } = useAuth();
  const [location, setLocation] = useLocation();

  // Redirect to login page when user logs out
  useEffect(() => {
    if (!isLoading && !isAuthenticated && location !== "/") {
      console.log("🔓 User not authenticated, redirecting to login");
      setLocation("/");
    }
  }, [isAuthenticated, isLoading, location, setLocation]);

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-gray-600 text-sm">Loading...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return <Login />;
  }

  return (
    <Switch>
      <Route path="/" component={() => isAdmin ? <ProtectedRoute component={AdminDashboard} /> : <ProtectedRoute component={StudentDashboard} />} />
      <Route path="/admin" component={() => <ProtectedRoute component={AdminDashboard} requireAdmin />} />
      <Route path="/student" component={() => <ProtectedRoute component={StudentDashboard} />} />
      <Route path="/employees" component={() => <ProtectedRoute component={EmployeeManagement} requireAdmin />} />
      <Route path="/attendance" component={() => <ProtectedRoute component={AttendanceLogs} />} />
      <Route path="/admin/recognition" component={() => <ProtectedRoute component={FrownRecognition} requireAdmin />} />
      <Route path="/student/checkin" component={() => <ProtectedRoute component={FrownRecognition} />} />
      <Route path="/admin/reports" component={() => <ProtectedRoute component={Reports} requireAdmin />} />
      <Route path="/admin/settings" component={() => <ProtectedRoute component={Settings} requireAdmin />} />
      <Route path="/system-test" component={() => <ProtectedRoute component={SystemTest} requireAdmin />} />
      <Route path="/logout-test" component={() => <ProtectedRoute component={LogoutTest} />} />
      <Route path="/quick-actions-test" component={() => <ProtectedRoute component={QuickActionsTest} />} />
      <Route path="/camera-test" component={CameraTest} />
      <Route component={NotFound} />
    </Switch>
  );
}

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <Toaster />
        <Router />
      </TooltipProvider>
    </QueryClientProvider>
  );
}

export default App;
