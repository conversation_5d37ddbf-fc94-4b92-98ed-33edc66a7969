import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useCamera } from '@/hooks/use-camera';
import { Camera, CameraOff, Download, Info } from 'lucide-react';

export function CameraTest() {
  const [capturedImage, setCapturedImage] = useState<string | null>(null);
  const [capabilities, setCapabilities] = useState<any>(null);
  
  const {
    isLoading,
    isActive,
    error,
    videoRef,
    canvasRef,
    checkCapabilities,
    startCamera,
    stopCamera,
    captureImage,
  } = useCamera();

  const handleCheckCapabilities = async () => {
    const caps = await checkCapabilities();
    setCapabilities(caps);
    console.log('Camera capabilities:', caps);
  };

  const handleCapture = async () => {
    try {
      const imageData = await captureImage();
      setCapturedImage(imageData);
      console.log('Captured image data length:', imageData.length);
    } catch (error) {
      console.error('Capture failed:', error);
    }
  };

  const downloadImage = () => {
    if (capturedImage) {
      const link = document.createElement('a');
      link.href = capturedImage;
      link.download = `camera-test-${Date.now()}.jpg`;
      link.click();
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Camera className="h-5 w-5 mr-2" />
            Camera Functionality Test
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Capabilities Check */}
          <div className="space-y-2">
            <Button onClick={handleCheckCapabilities} variant="outline">
              <Info className="h-4 w-4 mr-2" />
              Check Camera Capabilities
            </Button>
            {capabilities && (
              <div className="p-3 bg-gray-50 rounded-lg text-sm">
                <p><strong>Has Camera:</strong> {capabilities.hasCamera ? 'Yes' : 'No'}</p>
                <p><strong>Has Permission:</strong> {capabilities.hasPermission === null ? 'Unknown' : capabilities.hasPermission ? 'Yes' : 'No'}</p>
                <p><strong>Supported Constraints:</strong> {capabilities.supportedConstraints ? Object.keys(capabilities.supportedConstraints).length : 0} features</p>
              </div>
            )}
          </div>

          {/* Camera Controls */}
          <div className="flex space-x-3">
            <Button
              onClick={() => startCamera()}
              disabled={isLoading || isActive}
              className="flex items-center"
            >
              <Camera className="h-4 w-4 mr-2" />
              Start Camera
            </Button>
            <Button
              onClick={() => stopCamera()}
              disabled={!isActive}
              variant="outline"
              className="flex items-center"
            >
              <CameraOff className="h-4 w-4 mr-2" />
              Stop Camera
            </Button>
            <Button
              onClick={handleCapture}
              disabled={!isActive}
              variant="secondary"
              className="flex items-center"
            >
              <Camera className="h-4 w-4 mr-2" />
              Capture Photo
            </Button>
          </div>

          {/* Camera Display */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Live Video */}
            <div>
              <h3 className="text-lg font-semibold mb-3">Live Camera Feed</h3>
              <div className="relative aspect-video bg-gray-100 rounded-lg overflow-hidden">
                <canvas ref={canvasRef} className="hidden" />
                {isActive ? (
                  <video
                    ref={videoRef}
                    autoPlay
                    playsInline
                    muted
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center text-gray-500">
                    {isLoading ? 'Starting camera...' : 'Camera not active'}
                  </div>
                )}
                
                {error && (
                  <div className="absolute inset-0 bg-red-50 flex items-center justify-center p-4">
                    <div className="text-center">
                      <p className="text-red-800 font-medium">{error.type}</p>
                      <p className="text-red-600 text-sm">{error.message}</p>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Captured Image */}
            <div>
              <h3 className="text-lg font-semibold mb-3">Captured Image</h3>
              <div className="relative aspect-video bg-gray-100 rounded-lg overflow-hidden">
                {capturedImage ? (
                  <img 
                    src={capturedImage} 
                    alt="Captured" 
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center text-gray-500">
                    No image captured yet
                  </div>
                )}
              </div>
              {capturedImage && (
                <Button 
                  onClick={downloadImage}
                  className="mt-3 w-full"
                  variant="outline"
                >
                  <Download className="h-4 w-4 mr-2" />
                  Download Image
                </Button>
              )}
            </div>
          </div>

          {/* Status Information */}
          <div className="p-4 bg-blue-50 rounded-lg">
            <h4 className="font-medium text-blue-900 mb-2">Camera Status</h4>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-blue-700">Loading:</span> 
                <span className={`ml-2 ${isLoading ? 'text-orange-600' : 'text-green-600'}`}>
                  {isLoading ? 'Yes' : 'No'}
                </span>
              </div>
              <div>
                <span className="text-blue-700">Active:</span> 
                <span className={`ml-2 ${isActive ? 'text-green-600' : 'text-gray-600'}`}>
                  {isActive ? 'Yes' : 'No'}
                </span>
              </div>
              <div>
                <span className="text-blue-700">Error:</span> 
                <span className={`ml-2 ${error ? 'text-red-600' : 'text-green-600'}`}>
                  {error ? error.type : 'None'}
                </span>
              </div>
              <div>
                <span className="text-blue-700">Image Captured:</span> 
                <span className={`ml-2 ${capturedImage ? 'text-green-600' : 'text-gray-600'}`}>
                  {capturedImage ? 'Yes' : 'No'}
                </span>
              </div>
            </div>
          </div>

          {/* Instructions */}
          <div className="p-4 bg-yellow-50 rounded-lg">
            <h4 className="font-medium text-yellow-900 mb-2">Testing Instructions</h4>
            <ol className="text-sm text-yellow-800 space-y-1 list-decimal list-inside">
              <li>Click "Check Camera Capabilities" to verify browser support</li>
              <li>Click "Start Camera" and allow camera permissions when prompted</li>
              <li>Verify live video feed appears in the left panel</li>
              <li>Click "Capture Photo" to take a picture</li>
              <li>Verify captured image appears in the right panel</li>
              <li>Test "Download Image" to save the captured photo</li>
              <li>Click "Stop Camera" to release camera resources</li>
            </ol>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
