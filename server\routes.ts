import type { Express } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";
import { faceRecognitionService } from "./face-recognition";
import {
  loginSchema,
  insertUserSchema,
  insertAttendanceSchema,
  faceRecognitionSchema,
  faceEnrollmentSchema,
  imageQualitySchema
} from "@shared/schema";

export async function registerRoutes(app: Express): Promise<Server> {
  
  // Authentication routes
  app.post("/api/auth/login", async (req, res) => {
    try {
      const { username, password } = loginSchema.parse(req.body);
      
      const user = await storage.getUserByUsername(username);
      if (!user || user.password !== password) {
        return res.status(401).json({ message: "Invalid credentials" });
      }

      // In production, use proper session management
      res.json({
        user: {
          id: user.id,
          username: user.username,
          fullName: user.fullName,
          role: user.role,
          department: user.department,
          employeeId: user.employeeId,
        }
      });
    } catch (error) {
      res.status(400).json({ message: "Invalid request data" });
    }
  });

  // Logout route
  app.post("/api/auth/logout", async (req, res) => {
    try {
      // In a real application, you would invalidate the session/token here
      res.json({ message: "Logged out successfully" });
    } catch (error) {
      res.status(500).json({ message: "Logout failed" });
    }
  });

  // Session validation route
  app.get("/api/auth/me", async (req, res) => {
    try {
      // In a real application, you would validate the session/token here
      // For now, we'll just return a success response since we're using client-side auth
      res.json({ message: "Session valid" });
    } catch (error) {
      res.status(401).json({ message: "Session invalid" });
    }
  });

  // User management routes
  app.get("/api/users", async (req, res) => {
    try {
      const users = await storage.getAllUsers();
      res.json(users.map(user => ({
        id: user.id,
        username: user.username,
        fullName: user.fullName,
        role: user.role,
        department: user.department,
        employeeId: user.employeeId,
        isActive: user.isActive,
      })));
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch users" });
    }
  });

  app.post("/api/users", async (req, res) => {
    try {
      const userData = insertUserSchema.parse(req.body);
      const user = await storage.createUser(userData);
      res.status(201).json({
        id: user.id,
        username: user.username,
        fullName: user.fullName,
        role: user.role,
        department: user.department,
        employeeId: user.employeeId,
      });
    } catch (error) {
      res.status(400).json({ message: "Failed to create user" });
    }
  });

  // Face recognition route
  app.post("/api/face-recognition", async (req, res) => {
    try {
      const { imageData } = faceRecognitionSchema.parse(req.body);

      console.log('🔍 Face recognition request received');

      // Initialize face recognition service if needed
      await faceRecognitionService.initialize();

      // Perform real face recognition
      const result = await faceRecognitionService.recognizeFace(imageData);

      if (!result.recognized) {
        return res.status(404).json({
          recognized: false,
          message: result.error || "Face not recognized"
        });
      }

      res.json(result);
    } catch (error) {
      console.error('❌ Face recognition error:', error);
      res.status(500).json({
        recognized: false,
        message: "Face recognition service error"
      });
    }
  });

  // Frown recognition route (enhanced face recognition with emotion detection)
  app.post("/api/frown-recognition", async (req, res) => {
    try {
      const { imageData } = faceRecognitionSchema.parse(req.body);

      console.log('😊 Emotion recognition request received');

      // Initialize face recognition service if needed
      await faceRecognitionService.initialize();

      // Perform real face and emotion recognition
      const result = await faceRecognitionService.recognizeEmotions(imageData);

      if (!result.recognized) {
        return res.status(404).json({
          recognized: false,
          message: result.error || "Face not recognized"
        });
      }

      res.json(result);
    } catch (error) {
      console.error('❌ Emotion recognition error:', error);
      res.status(500).json({
        recognized: false,
        message: "Emotion recognition service error"
      });
    }
  });

  // Face enrollment route
  app.post("/api/face-enrollment", async (req, res) => {
    try {
      const { userId, imageData } = faceEnrollmentSchema.parse(req.body);

      console.log(`👤 Face enrollment request for user ${userId}`);

      // Verify user exists
      const user = await storage.getUser(userId);
      if (!user) {
        return res.status(404).json({ message: "User not found" });
      }

      // Initialize face recognition service if needed
      await faceRecognitionService.initialize();

      // Validate image quality first
      const qualityResult = await faceRecognitionService.validateImageQuality(imageData);
      if (!qualityResult.isValid) {
        return res.status(400).json({
          success: false,
          message: "Image quality insufficient",
          issues: qualityResult.issues,
          score: qualityResult.score
        });
      }

      // Enroll the face
      const enrollmentResult = await faceRecognitionService.enrollFace(userId, imageData);

      if (!enrollmentResult.success) {
        return res.status(400).json(enrollmentResult);
      }

      res.json({
        success: true,
        message: `Face enrolled successfully for ${user.fullName}`,
        qualityScore: qualityResult.score
      });
    } catch (error) {
      console.error('❌ Face enrollment error:', error);
      res.status(500).json({
        success: false,
        message: "Face enrollment service error"
      });
    }
  });

  // Image quality validation route
  app.post("/api/image-quality", async (req, res) => {
    try {
      const { imageData } = imageQualitySchema.parse(req.body);

      console.log('🔍 Image quality validation request');

      // Initialize face recognition service if needed
      await faceRecognitionService.initialize();

      // Validate image quality
      const result = await faceRecognitionService.validateImageQuality(imageData);

      res.json(result);
    } catch (error) {
      console.error('❌ Image quality validation error:', error);
      res.status(500).json({
        isValid: false,
        issues: ["Image quality validation failed"],
        score: 0
      });
    }
  });

  // Multiple face detection route
  app.post("/api/detect-faces", async (req, res) => {
    try {
      const { imageData } = imageQualitySchema.parse(req.body);

      console.log('👥 Multiple face detection request');

      // Initialize face recognition service if needed
      await faceRecognitionService.initialize();

      // Detect multiple faces
      const result = await faceRecognitionService.detectMultipleFaces(imageData);

      res.json(result);
    } catch (error) {
      console.error('❌ Multiple face detection error:', error);
      res.status(500).json({
        faceCount: 0,
        faces: []
      });
    }
  });

  // Attendance routes
  app.post("/api/attendance/checkin", async (req, res) => {
    try {
      const { userId, location = "Main Office" } = req.body;
      
      if (!userId) {
        return res.status(400).json({ message: "User ID is required" });
      }

      const user = await storage.getUser(userId);
      if (!user) {
        return res.status(404).json({ message: "User not found" });
      }

      // Check if user already checked in today
      const today = new Date().toISOString().split('T')[0];
      const todayRecords = await storage.getAttendanceByDate(today);
      const existingRecord = todayRecords.find(r => r.userId === userId);
      
      if (existingRecord && !existingRecord.checkOutTime) {
        return res.status(400).json({ message: "Already checked in today" });
      }

      const now = new Date();
      const workStartTime = new Date();
      workStartTime.setHours(9, 0, 0, 0); // 9:00 AM

      const status = now > workStartTime ? "late" : "present";

      const attendanceRecord = await storage.createAttendanceRecord({
        userId,
        checkInTime: now,
        checkOutTime: null,
        status,
        location,
        recognitionConfidence: Math.floor(Math.random() * 20) + 80,
      });

      res.json(attendanceRecord);
    } catch (error) {
      res.status(500).json({ message: "Failed to record check-in" });
    }
  });

  app.post("/api/attendance/checkout", async (req, res) => {
    try {
      const { userId } = req.body;
      
      if (!userId) {
        return res.status(400).json({ message: "User ID is required" });
      }

      const today = new Date().toISOString().split('T')[0];
      const todayRecords = await storage.getAttendanceByDate(today);
      const activeRecord = todayRecords.find(r => r.userId === userId && !r.checkOutTime);
      
      if (!activeRecord) {
        return res.status(400).json({ message: "No active check-in found" });
      }

      const updatedRecord = await storage.updateAttendanceRecord(activeRecord.id, {
        checkOutTime: new Date(),
      });

      res.json(updatedRecord);
    } catch (error) {
      res.status(500).json({ message: "Failed to record check-out" });
    }
  });

  app.get("/api/attendance/stats", async (req, res) => {
    try {
      const stats = await storage.getTodayAttendanceStats();
      res.json(stats);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch attendance stats" });
    }
  });

  app.get("/api/attendance/recent", async (req, res) => {
    try {
      const records = await storage.getAllAttendanceRecords();
      const users = await storage.getAllUsers();
      
      // Get recent records (last 10)
      const recentRecords = records
        .sort((a, b) => new Date(b.checkInTime).getTime() - new Date(a.checkInTime).getTime())
        .slice(0, 10);

      const recentWithUserInfo = recentRecords.map(record => {
        const user = users.find(u => u.id === record.userId);
        return {
          ...record,
          user: user ? {
            fullName: user.fullName,
            department: user.department,
            employeeId: user.employeeId,
          } : null,
        };
      });

      res.json(recentWithUserInfo);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch recent attendance" });
    }
  });

  app.get("/api/attendance/user/:userId", async (req, res) => {
    try {
      const userId = parseInt(req.params.userId);
      const records = await storage.getAttendanceByUserId(userId);
      res.json(records);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch user attendance" });
    }
  });

  const httpServer = createServer(app);
  return httpServer;
}
