@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: hsl(0, 0%, 100%);
  --foreground: hsl(240, 10%, 3.9%);
  --muted: hsl(240, 4.8%, 95.9%);
  --muted-foreground: hsl(240, 3.8%, 46.1%);
  --popover: hsl(0, 0%, 100%);
  --popover-foreground: hsl(240, 10%, 3.9%);
  --card: hsl(0, 0%, 100%);
  --card-foreground: hsl(240, 10%, 3.9%);
  --border: hsl(240, 5.9%, 90%);
  --input: hsl(240, 5.9%, 90%);
  --primary: hsl(207, 90%, 54%);
  --primary-foreground: hsl(0, 0%, 98%);
  --secondary: hsl(142, 76%, 36%);
  --secondary-foreground: hsl(0, 0%, 98%);
  --accent: hsl(240, 4.8%, 95.9%);
  --accent-foreground: hsl(240, 5.9%, 10%);
  --destructive: hsl(0, 84%, 60%);
  --destructive-foreground: hsl(0, 0%, 98%);
  --ring: hsl(240, 5%, 64.9%);
  --radius: 0.5rem;
  --surface: hsl(240, 9%, 98%);
  --error: hsl(0, 73%, 51%);
}

.dark {
  --background: hsl(240, 10%, 3.9%);
  --foreground: hsl(0, 0%, 98%);
  --muted: hsl(240, 3.7%, 15.9%);
  --muted-foreground: hsl(240, 5%, 64.9%);
  --popover: hsl(240, 10%, 3.9%);
  --popover-foreground: hsl(0, 0%, 98%);
  --card: hsl(240, 10%, 3.9%);
  --card-foreground: hsl(0, 0%, 98%);
  --border: hsl(240, 3.7%, 15.9%);
  --input: hsl(240, 3.7%, 15.9%);
  --primary: hsl(207, 90%, 54%);
  --primary-foreground: hsl(0, 0%, 98%);
  --secondary: hsl(142, 76%, 36%);
  --secondary-foreground: hsl(0, 0%, 98%);
  --accent: hsl(240, 3.7%, 15.9%);
  --accent-foreground: hsl(0, 0%, 98%);
  --destructive: hsl(0, 62.8%, 30.6%);
  --destructive-foreground: hsl(0, 0%, 98%);
  --ring: hsl(240, 4.9%, 83.9%);
  --surface: hsl(240, 10%, 3.9%);
  --error: hsl(0, 73%, 51%);
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply font-sans antialiased bg-surface text-foreground;
    font-family: 'Roboto', system-ui, -apple-system, sans-serif;
  }
}

@layer utilities {
  .bg-surface {
    background-color: hsl(var(--surface));
  }
  
  .text-error {
    color: hsl(var(--error));
  }
  
  .bg-error {
    background-color: hsl(var(--error));
  }
  
  .bg-grid-pattern {
    background-image: radial-gradient(circle at 1px 1px, rgba(255,255,255,0.15) 1px, transparent 0);
    background-size: 20px 20px;
  }
  
  .gradient-bg {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }
  
  .glass-morphism {
    background: rgba(255, 255, 255, 0.25);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.18);
  }
}
