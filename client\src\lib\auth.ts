import { apiRequest } from "./queryClient";
import type { LoginCredentials, User } from "@shared/schema";

interface AuthUser {
  id: number;
  username: string;
  fullName: string;
  role: string;
  department?: string;
  employeeId?: string;
}

class AuthService {
  private currentUser: AuthUser | null = null;

  async login(credentials: LoginCredentials): Promise<AuthUser> {
    try {
      const response = await apiRequest("POST", "/api/auth/login", credentials);
      if (!response.ok) {
        throw new Error(`Login failed: ${response.status}`);
      }
      const data = await response.json();
      this.currentUser = data.user;
      localStorage.setItem("user", JSON.stringify(data.user));
      return data.user;
    } catch (error) {
      console.error("Login error:", error);
      throw error;
    }
  }

  async logout(): Promise<void> {
    console.log("🔓 AuthService logout called");
    try {
      // Call logout endpoint to invalidate session on server
      console.log("🔓 Calling logout API...");
      await apiRequest("POST", "/api/auth/logout", {});
      console.log("🔓 Logout API call successful");
    } catch (error) {
      console.error("🔓 Logout API call failed:", error);
      // Continue with client-side logout even if server call fails
    } finally {
      // Always clear client-side state
      console.log("🔓 Clearing client-side state...");
      this.currentUser = null;
      localStorage.removeItem("user");
      console.log("🔓 Client-side state cleared");
    }
  }

  getCurrentUser(): AuthUser | null {
    if (this.currentUser) return this.currentUser;
    
    try {
      const storedUser = localStorage.getItem("user");
      if (storedUser) {
        this.currentUser = JSON.parse(storedUser);
      }
    } catch (error) {
      console.error("Error parsing stored user data:", error);
      localStorage.removeItem("user");
      this.currentUser = null;
    }
    
    return this.currentUser;
  }

  isAuthenticated(): boolean {
    return this.getCurrentUser() !== null;
  }

  isAdmin(): boolean {
    const user = this.getCurrentUser();
    return user?.role === "admin";
  }

  isStudent(): boolean {
    const user = this.getCurrentUser();
    return user?.role === "student";
  }
}

export const authService = new AuthService();
