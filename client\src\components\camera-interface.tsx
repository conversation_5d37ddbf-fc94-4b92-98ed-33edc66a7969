import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { useCamera } from '@/hooks/use-camera';
import { useToast } from '@/hooks/use-toast';
import { 
  Camera, 
  CameraOff, 
  Loader2, 
  AlertCircle, 
  Settings,
  RefreshCw,
  Sparkles 
} from 'lucide-react';

interface CameraInterfaceProps {
  onCapture: (imageData: string) => void;
  isProcessing?: boolean;
  className?: string;
  variant?: 'standard' | 'emotion';
}

export function CameraInterface({ 
  onCapture, 
  isProcessing = false, 
  className = '',
  variant = 'standard'
}: CameraInterfaceProps) {
  const [isCapturing, setIsCapturing] = useState(false);
  const [showPermissionHelp, setShowPermissionHelp] = useState(false);
  const { toast } = useToast();
  
  const {
    isLoading,
    isActive,
    error,
    videoRef,
    canvasRef,
    checkCapabilities,
    startCamera,
    stopCamera,
    captureImage,
  } = useCamera();

  // Check capabilities on mount
  useEffect(() => {
    checkCapabilities().then(capabilities => {
      if (!capabilities.hasCamera) {
        toast({
          title: "No Camera Found",
          description: "Please connect a camera to use face recognition.",
          variant: "destructive",
        });
      }
    });
  }, [checkCapabilities, toast]);

  const handleStartCamera = async () => {
    try {
      await startCamera();
      setShowPermissionHelp(false);
    } catch (err) {
      console.error('Failed to start camera:', err);
    }
  };

  const handleStopCamera = () => {
    stopCamera();
  };

  const handleCapture = async () => {
    if (!isActive) {
      toast({
        title: "Camera Not Active",
        description: "Please start the camera first.",
        variant: "destructive",
      });
      return;
    }

    setIsCapturing(true);
    try {
      const imageData = await captureImage();
      onCapture(imageData);
      
      toast({
        title: "Photo Captured",
        description: "Processing image for face recognition...",
      });
    } catch (err: any) {
      console.error('Capture failed:', err);
      toast({
        title: "Capture Failed",
        description: err.message || "Failed to capture image.",
        variant: "destructive",
      });
    } finally {
      setIsCapturing(false);
    }
  };

  const handleRetryPermission = () => {
    setShowPermissionHelp(false);
    handleStartCamera();
  };

  const getVariantColors = () => {
    if (variant === 'emotion') {
      return {
        gradient: 'from-purple-50 to-pink-100',
        border: 'border-purple-200',
        button: 'from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700',
        text: 'text-purple-700',
        subtext: 'text-purple-500',
      };
    }
    return {
      gradient: 'from-blue-50 to-indigo-100',
      border: 'border-blue-200',
      button: 'from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700',
      text: 'text-blue-700',
      subtext: 'text-blue-500',
    };
  };

  const colors = getVariantColors();

  return (
    <div className={`relative ${className}`}>
      {/* Hidden canvas for image capture */}
      <canvas ref={canvasRef} className="hidden" />
      
      {/* Camera viewport */}
      <div className={`aspect-video bg-gradient-to-br ${colors.gradient} rounded-xl border-2 border-dashed ${colors.border} flex flex-col items-center justify-center overflow-hidden relative`}>
        {/* Background pattern */}
        <div className="absolute inset-0 bg-grid-pattern opacity-10"></div>
        
        {/* Video element */}
        {isActive && (
          <video
            ref={videoRef}
            autoPlay
            playsInline
            muted
            className="absolute inset-0 w-full h-full object-cover rounded-lg"
          />
        )}
        
        {/* Loading state */}
        {isLoading && (
          <div className="text-center relative z-10">
            <div className="relative">
              <div className={`animate-spin rounded-full h-12 w-12 border-4 border-opacity-20 border-t-opacity-100 mx-auto mb-4 ${colors.text.replace('text-', 'border-')}`}></div>
              <Sparkles className={`absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 h-5 w-5 ${colors.text}`} />
            </div>
            <p className={`${colors.text} text-sm font-medium`}>Starting camera...</p>
            <p className={`${colors.subtext} text-xs mt-1`}>Please allow camera access</p>
          </div>
        )}
        
        {/* Processing state */}
        {isProcessing && (
          <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center z-20">
            <div className="text-center text-white">
              <div className="relative">
                <div className="animate-spin rounded-full h-12 w-12 border-4 border-white border-opacity-20 border-t-white mx-auto mb-4"></div>
                <Sparkles className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 h-5 w-5 text-white" />
              </div>
              <p className="text-sm font-medium">
                {variant === 'emotion' ? 'Analyzing emotions...' : 'Processing face recognition...'}
              </p>
              <p className="text-xs mt-1 opacity-80">Please hold still</p>
            </div>
          </div>
        )}
        
        {/* Error state */}
        {error && !isActive && (
          <div className="text-center relative z-10 max-w-sm mx-auto p-4">
            <div className="bg-red-100 p-3 rounded-full mb-4 mx-auto w-fit">
              <AlertCircle className="h-8 w-8 text-red-600" />
            </div>
            <p className="text-red-800 text-sm font-medium mb-2">Camera Access Error</p>
            <p className="text-red-600 text-xs mb-4">{error.message}</p>
            
            {error.type === 'permission' && (
              <div className="space-y-2">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={handleRetryPermission}
                  className="text-red-600 border-red-200 hover:bg-red-50"
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Retry Permission
                </Button>
                <p className="text-xs text-gray-500">
                  Click the camera icon in your browser's address bar to allow access
                </p>
              </div>
            )}
            
            {error.type === 'not_found' && (
              <Button
                size="sm"
                variant="outline"
                onClick={handleRetryPermission}
                className="text-red-600 border-red-200 hover:bg-red-50"
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Check Again
              </Button>
            )}
          </div>
        )}
        
        {/* Inactive state */}
        {!isLoading && !isActive && !error && (
          <div className="text-center relative z-10">
            <div className="bg-white/80 p-4 rounded-full mb-4 shadow-lg">
              <Camera className={`h-12 w-12 ${colors.text}`} />
            </div>
            <p className={`text-gray-700 text-sm font-medium`}>
              {variant === 'emotion' ? 'Position your face for emotion analysis' : 'Position your face in the frame'}
            </p>
            <p className="text-gray-500 text-xs mt-1">Click the camera button to start</p>
          </div>
        )}
        
        {/* Camera controls */}
        <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-3 z-30">
          {!isActive ? (
            <Button
              size="lg"
              className={`rounded-full bg-gradient-to-r ${colors.button} shadow-lg`}
              onClick={handleStartCamera}
              disabled={isLoading}
            >
              {isLoading ? (
                <Loader2 className="h-5 w-5 animate-spin" />
              ) : (
                <Camera className="h-5 w-5" />
              )}
            </Button>
          ) : (
            <>
              <Button
                size="lg"
                className={`rounded-full bg-gradient-to-r ${colors.button} shadow-lg`}
                onClick={handleCapture}
                disabled={isCapturing || isProcessing}
              >
                {isCapturing ? (
                  <Loader2 className="h-5 w-5 animate-spin" />
                ) : (
                  <Camera className="h-5 w-5" />
                )}
              </Button>
              
              <Button
                size="lg"
                variant="outline"
                className="rounded-full bg-white/80 backdrop-blur-sm border-white/40 hover:bg-white/90"
                onClick={handleStopCamera}
                disabled={isCapturing || isProcessing}
              >
                <CameraOff className="h-5 w-5" />
              </Button>
            </>
          )}
        </div>
        
        {/* Capture flash effect */}
        {isCapturing && (
          <div className="absolute inset-0 bg-white opacity-70 z-25 animate-pulse"></div>
        )}
      </div>
    </div>
  );
}
