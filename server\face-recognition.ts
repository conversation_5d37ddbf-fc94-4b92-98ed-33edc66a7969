import * as faceapi from 'face-api.js';
import { Canvas, Image, ImageData } from 'canvas';
import * as tf from '@tensorflow/tfjs-node';
import { storage } from './storage';
import type { User } from '@shared/schema';

// Patch face-api.js to work with Node.js
faceapi.env.monkeyPatch({
  Canvas: Canvas as any,
  Image: Image as any,
  ImageData: ImageData as any,
  createCanvasElement: () => new Canvas(1, 1) as any,
  createImageElement: () => new Image() as any,
});

export interface FaceDescriptor {
  userId: number;
  descriptor: Float32Array;
  enrollmentDate: Date;
}

export interface FaceRecognitionResult {
  recognized: boolean;
  user?: {
    id: number;
    fullName: string;
    employeeId: string;
    department: string;
  };
  confidence?: number;
  error?: string;
}

export interface EmotionRecognitionResult extends FaceRecognitionResult {
  emotion?: string;
  emotionConfidence?: number;
  emotions?: { [key: string]: number };
}

export class FaceRecognitionService {
  private isInitialized = false;
  private faceDescriptors: Map<number, FaceDescriptor> = new Map();
  private readonly CONFIDENCE_THRESHOLD = 0.6;
  private readonly EMOTION_THRESHOLD = 0.5;

  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      console.log('🤖 Initializing Face Recognition Service...');
      
      // Load face-api.js models
      await faceapi.nets.ssdMobilenetv1.loadFromDisk('./models');
      await faceapi.nets.faceLandmark68Net.loadFromDisk('./models');
      await faceapi.nets.faceRecognitionNet.loadFromDisk('./models');
      await faceapi.nets.faceExpressionNet.loadFromDisk('./models');
      
      console.log('✅ Face recognition models loaded successfully');
      
      // Load existing face descriptors from storage
      await this.loadFaceDescriptors();
      
      this.isInitialized = true;
      console.log('🎯 Face Recognition Service initialized');
    } catch (error) {
      console.error('❌ Failed to initialize Face Recognition Service:', error);
      throw new Error('Face recognition initialization failed');
    }
  }

  private async loadFaceDescriptors(): Promise<void> {
    try {
      console.log('📂 Loading face descriptors from storage...');

      // Load face descriptors from all enrolled users
      const users = await storage.getAllUsers();
      let loadedCount = 0;

      for (const user of users) {
        if (user.faceEnrolled && user.faceDescriptor) {
          try {
            const descriptorArray = JSON.parse(user.faceDescriptor);
            const descriptor = new Float32Array(descriptorArray);

            const faceDescriptor: FaceDescriptor = {
              userId: user.id,
              descriptor,
              enrollmentDate: user.enrollmentDate || new Date(),
            };

            this.faceDescriptors.set(user.id, faceDescriptor);
            loadedCount++;
          } catch (error) {
            console.error(`❌ Failed to parse face descriptor for user ${user.id}:`, error);
          }
        }
      }

      console.log(`📊 Loaded ${loadedCount} face descriptors from ${users.length} users`);
    } catch (error) {
      console.error('❌ Failed to load face descriptors:', error);
    }
  }

  private async saveFaceDescriptor(userId: number, descriptor: Float32Array): Promise<void> {
    try {
      const faceDescriptor: FaceDescriptor = {
        userId,
        descriptor,
        enrollmentDate: new Date(),
      };

      // Save to memory
      this.faceDescriptors.set(userId, faceDescriptor);

      // Save to persistent storage
      const descriptorArray = Array.from(descriptor);
      const descriptorJson = JSON.stringify(descriptorArray);

      await storage.updateUser(userId, {
        faceDescriptor: descriptorJson,
        faceEnrolled: true,
        enrollmentDate: new Date(),
      });

      console.log(`💾 Saved face descriptor for user ${userId}`);
    } catch (error) {
      console.error(`❌ Failed to save face descriptor for user ${userId}:`, error);
      throw error;
    }
  }

  private async base64ToCanvas(base64Data: string): Promise<Canvas> {
    try {
      // Remove data URL prefix if present
      const base64 = base64Data.replace(/^data:image\/[a-z]+;base64,/, '');
      const buffer = Buffer.from(base64, 'base64');

      const img = new Image();

      // Load image asynchronously
      await new Promise((resolve, reject) => {
        img.onload = resolve;
        img.onerror = reject;
        img.src = buffer;
      });

      const canvas = new Canvas(img.width, img.height);
      const ctx = canvas.getContext('2d');
      ctx.drawImage(img, 0, 0);

      return canvas;
    } catch (error) {
      console.error('❌ Failed to convert base64 to canvas:', error);
      throw new Error('Invalid image data');
    }
  }

  async enrollFace(userId: number, imageData: string): Promise<{ success: boolean; error?: string }> {
    try {
      if (!this.isInitialized) {
        await this.initialize();
      }

      console.log(`👤 Enrolling face for user ${userId}...`);

      const canvas = await this.base64ToCanvas(imageData);
      
      // Detect face and extract features
      const detection = await faceapi
        .detectSingleFace(canvas as any)
        .withFaceLandmarks()
        .withFaceDescriptor();

      if (!detection) {
        return { success: false, error: 'No face detected in image' };
      }

      // Save the face descriptor
      await this.saveFaceDescriptor(userId, detection.descriptor);
      
      console.log(`✅ Face enrolled successfully for user ${userId}`);
      return { success: true };
    } catch (error) {
      console.error(`❌ Face enrollment failed for user ${userId}:`, error);
      return { success: false, error: 'Face enrollment failed' };
    }
  }

  async recognizeFace(imageData: string): Promise<FaceRecognitionResult> {
    try {
      if (!this.isInitialized) {
        await this.initialize();
      }

      console.log('🔍 Starting face recognition...');

      const canvas = await this.base64ToCanvas(imageData);
      
      // Detect face and extract features
      const detection = await faceapi
        .detectSingleFace(canvas as any)
        .withFaceLandmarks()
        .withFaceDescriptor();

      if (!detection) {
        console.log('❌ No face detected in image');
        return { recognized: false, error: 'No face detected in image' };
      }

      // If no enrolled faces, return unrecognized
      if (this.faceDescriptors.size === 0) {
        console.log('⚠️ No enrolled faces in database');
        return { recognized: false, error: 'No enrolled faces in database' };
      }

      // Compare with enrolled faces
      let bestMatch: { userId: number; distance: number } | null = null;
      
      for (const [userId, faceDescriptor] of this.faceDescriptors) {
        const distance = faceapi.euclideanDistance(detection.descriptor, faceDescriptor.descriptor);
        
        if (!bestMatch || distance < bestMatch.distance) {
          bestMatch = { userId, distance };
        }
      }

      if (!bestMatch || bestMatch.distance > this.CONFIDENCE_THRESHOLD) {
        console.log(`❌ No matching face found (best distance: ${bestMatch?.distance})`);
        return { recognized: false, error: 'Face not recognized' };
      }

      // Get user information
      const user = await storage.getUser(bestMatch.userId);
      if (!user) {
        console.log(`❌ User ${bestMatch.userId} not found in database`);
        return { recognized: false, error: 'User not found' };
      }

      const confidence = Math.max(0, Math.min(100, Math.round((1 - bestMatch.distance) * 100)));
      
      console.log(`✅ Face recognized: ${user.fullName} (confidence: ${confidence}%)`);
      
      return {
        recognized: true,
        user: {
          id: user.id,
          fullName: user.fullName,
          employeeId: user.employeeId || '',
          department: user.department || '',
        },
        confidence,
      };
    } catch (error) {
      console.error('❌ Face recognition failed:', error);
      return { recognized: false, error: 'Face recognition failed' };
    }
  }

  async recognizeEmotions(imageData: string): Promise<EmotionRecognitionResult> {
    try {
      if (!this.isInitialized) {
        await this.initialize();
      }

      console.log('😊 Starting emotion recognition...');

      const canvas = await this.base64ToCanvas(imageData);
      
      // Detect face with emotions
      const detection = await faceapi
        .detectSingleFace(canvas as any)
        .withFaceLandmarks()
        .withFaceExpressions()
        .withFaceDescriptor();

      if (!detection) {
        console.log('❌ No face detected for emotion recognition');
        return { recognized: false, error: 'No face detected in image' };
      }

      // Get face recognition result first
      const faceResult = await this.recognizeFace(imageData);
      
      // Process emotions
      const expressions = detection.expressions;
      const emotions = {
        happy: expressions.happy,
        sad: expressions.sad,
        angry: expressions.angry,
        fearful: expressions.fearful,
        disgusted: expressions.disgusted,
        surprised: expressions.surprised,
        neutral: expressions.neutral,
      };

      // Find dominant emotion
      let dominantEmotion = 'neutral';
      let maxConfidence = 0;
      
      for (const [emotion, confidence] of Object.entries(emotions)) {
        if (confidence > maxConfidence) {
          maxConfidence = confidence;
          dominantEmotion = emotion;
        }
      }

      // Map to simplified emotion categories
      const emotionMapping: { [key: string]: string } = {
        happy: 'happy',
        sad: 'sad',
        angry: 'frown',
        fearful: 'frown',
        disgusted: 'frown',
        surprised: 'neutral',
        neutral: 'neutral',
      };

      const mappedEmotion = emotionMapping[dominantEmotion] || 'neutral';
      const emotionConfidence = Math.round(maxConfidence * 100);
      
      console.log(`😊 Emotion detected: ${mappedEmotion} (${emotionConfidence}%)`);
      
      return {
        ...faceResult,
        emotion: mappedEmotion,
        emotionConfidence,
        emotions,
      };
    } catch (error) {
      console.error('❌ Emotion recognition failed:', error);
      return { recognized: false, error: 'Emotion recognition failed' };
    }
  }

  async detectMultipleFaces(imageData: string): Promise<{
    faceCount: number;
    faces: Array<{
      confidence: number;
      boundingBox: { x: number; y: number; width: number; height: number };
    }>;
  }> {
    try {
      if (!this.isInitialized) {
        await this.initialize();
      }

      const canvas = await this.base64ToCanvas(imageData);

      const detections = await faceapi.detectAllFaces(canvas as any);
      
      const faces = detections.map(detection => ({
        confidence: detection.score,
        boundingBox: {
          x: detection.box.x,
          y: detection.box.y,
          width: detection.box.width,
          height: detection.box.height,
        },
      }));

      return {
        faceCount: faces.length,
        faces,
      };
    } catch (error) {
      console.error('❌ Multiple face detection failed:', error);
      return { faceCount: 0, faces: [] };
    }
  }

  async validateImageQuality(imageData: string): Promise<{
    isValid: boolean;
    issues: string[];
    score: number;
  }> {
    try {
      const canvas = await this.base64ToCanvas(imageData);
      const issues: string[] = [];
      let score = 100;

      // Check image dimensions
      if (canvas.width < 200 || canvas.height < 200) {
        issues.push('Image resolution too low');
        score -= 30;
      }

      // Detect faces for quality assessment
      const detections = await faceapi.detectAllFaces(canvas as any).withFaceLandmarks();

      if (detections.length === 0) {
        issues.push('No face detected');
        score = 0;
      } else if (detections.length > 1) {
        issues.push('Multiple faces detected - please use image with single face');
        score -= 40;
      }

      // Use the largest face for quality assessment
      const detection = detections.length > 0 ?
        detections.reduce((largest, current) => {
          const largestBox = largest.box || largest.detection?.box;
          const currentBox = current.box || current.detection?.box;
          if (!largestBox || !currentBox) return largest;
          const largestArea = largestBox.width * largestBox.height;
          const currentArea = currentBox.width * currentBox.height;
          return currentArea > largestArea ? current : largest;
        }) : null;

      if (detection) {
        // Debug: log detection structure
        console.log('🔍 Detection object keys:', Object.keys(detection));
        console.log('🔍 Detection box:', detection.box || detection.detection?.box);

        // Check face size
        const box = detection.box || detection.detection?.box;
        if (!box) {
          issues.push('Face detection box not found');
          score = 0;
        } else {
          const faceArea = box.width * box.height;
          const imageArea = canvas.width * canvas.height;
          const faceRatio = faceArea / imageArea;

          if (faceRatio < 0.1) {
            issues.push('Face too small in image');
            score -= 20;
          }

          if (faceRatio > 0.8) {
            issues.push('Face too close to camera');
            score -= 10;
          }

          // Check face position (should be roughly centered)
          const faceCenterX = box.x + box.width / 2;
          const faceCenterY = box.y + box.height / 2;
          const imageCenterX = canvas.width / 2;
          const imageCenterY = canvas.height / 2;

          const offsetX = Math.abs(faceCenterX - imageCenterX) / canvas.width;
          const offsetY = Math.abs(faceCenterY - imageCenterY) / canvas.height;

          if (offsetX > 0.3 || offsetY > 0.3) {
            issues.push('Face not centered in image');
            score -= 15;
          }
        }
      }

      return {
        isValid: score >= 60,
        issues,
        score: Math.max(0, score),
      };
    } catch (error) {
      console.error('❌ Image quality validation failed:', error);
      return {
        isValid: false,
        issues: ['Image validation failed'],
        score: 0,
      };
    }
  }
}

export const faceRecognitionService = new FaceRecognitionService();
