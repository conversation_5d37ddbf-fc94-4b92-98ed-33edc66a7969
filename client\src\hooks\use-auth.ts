import { useState, useEffect } from "react";
import { authService } from "@/lib/auth";

interface AuthUser {
  id: number;
  username: string;
  fullName: string;
  role: string;
  department?: string;
  employeeId?: string;
}

export function useAuth() {
  const [user, setUser] = useState<AuthUser | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isLoggingOut, setIsLoggingOut] = useState(false);

  useEffect(() => {
    // Clear any corrupted auth state on app startup
    try {
      const currentUser = authService.getCurrentUser();
      setUser(currentUser);
    } catch (error) {
      console.error("Auth state corruption, clearing localStorage:", error);
      localStorage.removeItem("user");
      setUser(null);
    }
    setIsLoading(false);
  }, []);

  const login = async (credentials: { username: string; password: string }) => {
    const user = await authService.login(credentials);
    setUser(user);
    return user;
  };

  const logout = async () => {
    if (isLoggingOut) {
      console.log("🔓 Logout already in progress, ignoring...");
      return;
    }

    console.log("🔓 Logout initiated");
    setIsLoggingOut(true);

    try {
      await authService.logout();
      console.log("🔓 Server logout successful");
      setUser(null);
      console.log("🔓 Client state cleared");
    } catch (error) {
      console.error("🔓 Logout failed:", error);
      // Force logout on client side even if server call fails
      setUser(null);
      console.log("🔓 Client state cleared (forced)");
    } finally {
      setIsLoggingOut(false);
      console.log("🔓 Logout process completed");
    }
  };

  return {
    user,
    isLoading,
    isLoggingOut,
    isAuthenticated: !!user,
    isAdmin: user?.role === "admin",
    isStudent: user?.role === "student",
    login,
    logout,
  };
}
