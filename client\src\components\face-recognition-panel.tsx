import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { useAuth } from "@/hooks/use-auth";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { CameraInterface } from "@/components/camera-interface";
import { Upload, Scan, CheckCircle } from "lucide-react";

interface RecognitionResult {
  recognized: boolean;
  user?: {
    id: number;
    fullName: string;
    employeeId: string;
    department: string;
  };
  confidence?: number;
}

export function FaceRecognitionPanel() {
  const [recognitionResult, setRecognitionResult] = useState<RecognitionResult | null>(null);
  const { user } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const recognitionMutation = useMutation({
    mutationFn: async (imageData: string) => {
      const response = await apiRequest("POST", "/api/face-recognition", { imageData });
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `HTTP ${response.status}`);
      }
      return await response.json();
    },
    onSuccess: (data) => {
      console.log("Recognition result:", data);
      setRecognitionResult(data);
      if (data.recognized) {
        toast({
          title: "Face Recognized",
          description: `Welcome, ${data.user.fullName}!`,
        });
      } else {
        toast({
          title: "Recognition Failed",
          description: "Face not recognized. Please try again.",
          variant: "destructive",
        });
      }
    },
    onError: (error: any) => {
      console.error("Recognition error:", error);
      toast({
        title: "Recognition Error",
        description: error.message || "Failed to process face recognition.",
        variant: "destructive",
      });
    },
  });

  const checkInMutation = useMutation({
    mutationFn: async (userId: number) => {
      const response = await apiRequest("POST", "/api/attendance/checkin", { userId });
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `HTTP ${response.status}`);
      }
      return await response.json();
    },
    onSuccess: () => {
      toast({
        title: "Check-in Successful",
        description: "Your attendance has been recorded.",
      });
      queryClient.invalidateQueries({ queryKey: ["/api/attendance/stats"] });
      queryClient.invalidateQueries({ queryKey: ["/api/attendance/recent"] });
      setRecognitionResult(null);
    },
    onError: (error: any) => {
      console.error("Check-in error:", error);
      toast({
        title: "Check-in Failed",
        description: error.message || "Failed to record attendance.",
        variant: "destructive",
      });
    },
  });

  const handleCameraCapture = (imageData: string) => {
    console.log("Camera captured image, starting recognition...");
    recognitionMutation.mutate(imageData);
  };

  const handleUpload = () => {
    // Create file input for image upload
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = 'image/*';
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        const reader = new FileReader();
        reader.onload = (event) => {
          const imageData = event.target?.result as string;
          recognitionMutation.mutate(imageData);
        };
        reader.readAsDataURL(file);
      }
    };
    input.click();
  };

  const handleCheckIn = () => {
    if (recognitionResult?.user) {
      console.log("Starting check-in for user:", recognitionResult.user);
      checkInMutation.mutate(recognitionResult.user.id);
    }
  };

  return (
    <Card className="bg-white/60 backdrop-blur-sm border border-white/20 shadow-lg">
      <CardHeader className="flex flex-row items-center justify-between pb-4">
        <CardTitle className="text-lg font-semibold flex items-center">
          <div className="bg-gradient-to-r from-blue-500 to-purple-600 p-2 rounded-lg mr-3">
            <Scan className="h-5 w-5 text-white" />
          </div>
          Face Recognition Check-in
        </CardTitle>
        <div className="flex items-center space-x-1 text-xs text-green-600 bg-green-50 px-2 py-1 rounded-full">
          <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
          <span>Live</span>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Camera Interface */}
        <CameraInterface
          onCapture={handleCameraCapture}
          isProcessing={recognitionMutation.isPending}
          variant="standard"
        />

        {/* Upload Button */}
        <div className="flex justify-center">
          <Button
            variant="outline"
            onClick={handleUpload}
            disabled={recognitionMutation.isPending}
            className="flex items-center space-x-2"
          >
            <Upload className="h-4 w-4" />
            <span>Upload Photo</span>
          </Button>
        </div>

        {/* Recognition Status */}
        {recognitionResult && (
          <div className="space-y-4">
            {recognitionResult.recognized && recognitionResult.user ? (
              <div className="bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-xl p-4">
                <div className="flex items-center space-x-3">
                  <div className="bg-green-500 p-2 rounded-full">
                    <CheckCircle className="h-5 w-5 text-white" />
                  </div>
                  <div>
                    <p className="text-sm font-semibold text-green-900">Face Recognized Successfully</p>
                    <p className="text-xs text-green-700">
                      {recognitionResult.user.fullName} - ID: {recognitionResult.user.employeeId}
                    </p>
                    <p className="text-xs text-green-600 mt-1">
                      Confidence: {recognitionResult.confidence}%
                    </p>
                  </div>
                </div>
              </div>
            ) : (
              <div className="bg-gradient-to-r from-red-50 to-rose-50 border border-red-200 rounded-xl p-4">
                <div className="flex items-center space-x-3">
                  <div className="bg-red-500 p-2 rounded-full">
                    <Scan className="h-5 w-5 text-white" />
                  </div>
                  <div className="text-sm text-red-800 font-medium">Face not recognized. Please try again.</div>
                </div>
              </div>
            )}
            
            {recognitionResult.recognized && recognitionResult.user && (
              <Button
                className="w-full h-12 bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white font-medium shadow-lg"
                onClick={handleCheckIn}
                disabled={checkInMutation.isPending}
              >
                {checkInMutation.isPending ? (
                  <div className="flex items-center space-x-2">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    <span>Recording Check-in...</span>
                  </div>
                ) : (
                  <>
                    <CheckCircle className="h-5 w-5 mr-2" />
                    Complete Check-in
                  </>
                )}
              </Button>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
