import { Card, CardContent } from "@/components/ui/card";
import { useQuery } from "@tanstack/react-query";
import { CheckCircle, Users, Clock, XCircle, TrendingUp, TrendingDown } from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";
import { useLocation } from "wouter";
import { useToast } from "@/hooks/use-toast";

interface AttendanceStats {
  present: number;
  absent: number;
  late: number;
  total: number;
}

export function StatsCards() {
  const [, setLocation] = useLocation();
  const { toast } = useToast();
  const { data: stats, isLoading } = useQuery<AttendanceStats>({
    queryKey: ["/api/attendance/stats"],
  });

  const handleCardClick = (cardType: string) => {
    console.log(`📊 Stats card clicked: ${cardType}`);
    switch (cardType) {
      case 'present':
        setLocation("/attendance");
        break;
      case 'absent':
        setLocation("/employees");
        break;
      case 'late':
        setLocation("/attendance");
        break;
      case 'total':
        setLocation("/employees");
        break;
      default:
        toast({
          title: "Feature Coming Soon",
          description: "This feature will be available in a future update.",
        });
    }
  };

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {[...Array(4)].map((_, i) => (
          <Card key={i}>
            <CardContent className="p-6">
              <Skeleton className="h-20 w-full" />
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (!stats) return null;

  const cardsData = [
    {
      title: "Present Today",
      value: stats.present,
      icon: CheckCircle,
      iconBg: "bg-green-100",
      iconColor: "text-green-600",
      valueColor: "text-green-600",
      trend: { value: "+12%", type: "up" as const, label: "vs yesterday" },
      type: "present",
    },
    {
      title: "Absent Today",
      value: stats.absent,
      icon: XCircle,
      iconBg: "bg-red-100",
      iconColor: "text-red-600",
      valueColor: "text-red-600",
      trend: { value: "-5%", type: "down" as const, label: "vs yesterday" },
      type: "absent",
    },
    {
      title: "Late Arrivals",
      value: stats.late,
      icon: Clock,
      iconBg: "bg-amber-100",
      iconColor: "text-amber-600",
      valueColor: "text-amber-600",
      trend: { value: "+3", type: "up" as const, label: "vs yesterday" },
      type: "late",
    },
    {
      title: "Total Employees",
      value: stats.total,
      icon: Users,
      iconBg: "bg-blue-100",
      iconColor: "text-blue-600",
      valueColor: "text-blue-600",
      trend: { value: "+5", type: "up" as const, label: "this month" },
      type: "total",
    },
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {cardsData.map((card, index) => (
        <Card
          key={index}
          className="border border-gray-200 cursor-pointer transition-all duration-200 hover:shadow-lg hover:scale-105 hover:border-gray-300"
          onClick={() => handleCardClick(card.type)}
        >
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">{card.title}</p>
                <p className={`text-2xl font-bold ${card.valueColor}`}>{card.value}</p>
              </div>
              <div className={`${card.iconBg} rounded-lg p-3`}>
                <card.icon className={`h-6 w-6 ${card.iconColor}`} />
              </div>
            </div>
            <div className="mt-4 flex items-center text-sm">
              {card.trend.type === "up" ? (
                <TrendingUp className={`h-4 w-4 mr-1 ${card.iconColor}`} />
              ) : (
                <TrendingDown className={`h-4 w-4 mr-1 ${card.iconColor}`} />
              )}
              <span className={`font-medium ${card.valueColor}`}>{card.trend.value}</span>
              <span className="text-gray-500 ml-1">{card.trend.label}</span>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
