import { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useAuth } from '@/hooks/use-auth';
import { useToast } from '@/hooks/use-toast';
import { 
  LogOut, 
  User, 
  CheckCircle, 
  XCircle, 
  Clock,
  AlertTriangle,
  RefreshCw
} from 'lucide-react';

export default function LogoutTest() {
  const { user, logout, isLoggingOut, isAuthenticated } = useAuth();
  const { toast } = useToast();
  const [testResults, setTestResults] = useState<string[]>([]);
  const [isRunningTest, setIsRunningTest] = useState(false);

  const addTestResult = (message: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const clearTestResults = () => {
    setTestResults([]);
  };

  const testLogout = async () => {
    setIsRunningTest(true);
    clearTestResults();
    
    addTestResult("🔓 Starting logout test...");
    
    try {
      // Check initial state
      addTestResult(`📊 Initial state - Authenticated: ${isAuthenticated}, User: ${user?.fullName}`);
      
      // Check localStorage before logout
      const userDataBefore = localStorage.getItem('user');
      addTestResult(`💾 LocalStorage before logout: ${userDataBefore ? 'Has user data' : 'No user data'}`);
      
      // Perform logout
      addTestResult("🔓 Calling logout function...");
      await logout();
      
      // Check state after logout
      setTimeout(() => {
        const userDataAfter = localStorage.getItem('user');
        addTestResult(`💾 LocalStorage after logout: ${userDataAfter ? 'Still has data (ERROR)' : 'Cleared successfully'}`);
        addTestResult(`📊 Final state - Authenticated: ${isAuthenticated}, User: ${user?.fullName || 'null'}`);
        
        if (!isAuthenticated && !userDataAfter) {
          addTestResult("✅ Logout test PASSED - User logged out successfully");
          toast({
            title: "Logout Test Passed",
            description: "User was successfully logged out",
          });
        } else {
          addTestResult("❌ Logout test FAILED - User still authenticated or data not cleared");
          toast({
            title: "Logout Test Failed",
            description: "Logout did not work as expected",
            variant: "destructive",
          });
        }
        setIsRunningTest(false);
      }, 1000);
      
    } catch (error) {
      addTestResult(`❌ Logout error: ${error}`);
      setIsRunningTest(false);
    }
  };

  const testMultipleLogouts = async () => {
    setIsRunningTest(true);
    clearTestResults();
    
    addTestResult("🔄 Testing multiple logout calls...");
    
    try {
      // Call logout multiple times rapidly
      addTestResult("🔓 Calling logout 5 times rapidly...");
      const promises = Array(5).fill(null).map((_, i) => {
        addTestResult(`🔓 Logout call ${i + 1}`);
        return logout();
      });
      
      await Promise.all(promises);
      addTestResult("✅ Multiple logout calls completed without errors");
      
    } catch (error) {
      addTestResult(`❌ Multiple logout error: ${error}`);
    } finally {
      setIsRunningTest(false);
    }
  };

  const checkAuthState = () => {
    clearTestResults();
    addTestResult("📊 Current Authentication State:");
    addTestResult(`- Authenticated: ${isAuthenticated}`);
    addTestResult(`- User: ${user ? JSON.stringify(user, null, 2) : 'null'}`);
    addTestResult(`- Logging out: ${isLoggingOut}`);
    
    const userData = localStorage.getItem('user');
    addTestResult(`- LocalStorage: ${userData ? 'Has data' : 'Empty'}`);
    
    if (userData) {
      try {
        const parsed = JSON.parse(userData);
        addTestResult(`- Stored user: ${parsed.fullName} (${parsed.role})`);
      } catch (e) {
        addTestResult(`- Invalid user data in localStorage`);
      }
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-red-50 to-pink-100 p-6">
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Header */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <LogOut className="h-6 w-6 mr-3 text-red-600" />
              Logout Functionality Test Suite
            </CardTitle>
            <div className="flex items-center space-x-4 text-sm">
              <div className="flex items-center space-x-2">
                <User className="h-4 w-4" />
                <span>User: {user?.fullName || 'Not logged in'} ({user?.role || 'N/A'})</span>
              </div>
              <Badge variant={isAuthenticated ? "default" : "destructive"}>
                {isAuthenticated ? "Authenticated" : "Not Authenticated"}
              </Badge>
              {isLoggingOut && (
                <Badge variant="secondary">
                  <Clock className="h-3 w-3 mr-1" />
                  Logging out...
                </Badge>
              )}
            </div>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <Button 
                onClick={testLogout}
                disabled={isRunningTest || !isAuthenticated}
                className="bg-red-600 hover:bg-red-700"
              >
                <LogOut className="h-4 w-4 mr-2" />
                Test Logout
              </Button>
              
              <Button 
                onClick={testMultipleLogouts}
                disabled={isRunningTest || !isAuthenticated}
                variant="outline"
                className="border-red-200 text-red-600 hover:bg-red-50"
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Test Multiple Calls
              </Button>
              
              <Button 
                onClick={checkAuthState}
                disabled={isRunningTest}
                variant="outline"
              >
                <CheckCircle className="h-4 w-4 mr-2" />
                Check State
              </Button>
              
              <Button 
                onClick={clearTestResults}
                disabled={isRunningTest}
                variant="ghost"
              >
                Clear Results
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Test Results */}
        <Card>
          <CardHeader>
            <CardTitle>Test Results</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 max-h-96 overflow-y-auto">
              {testResults.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  No test results yet. Click a test button to start.
                </div>
              ) : (
                testResults.map((result, index) => (
                  <div 
                    key={index} 
                    className={`p-2 rounded text-sm font-mono ${
                      result.includes('✅') ? 'bg-green-50 text-green-800' :
                      result.includes('❌') ? 'bg-red-50 text-red-800' :
                      result.includes('🔓') ? 'bg-blue-50 text-blue-800' :
                      result.includes('📊') ? 'bg-purple-50 text-purple-800' :
                      result.includes('💾') ? 'bg-yellow-50 text-yellow-800' :
                      'bg-gray-50 text-gray-800'
                    }`}
                  >
                    {result}
                  </div>
                ))
              )}
            </div>
          </CardContent>
        </Card>

        {/* Instructions */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <AlertTriangle className="h-5 w-5 mr-2 text-yellow-600" />
              Testing Instructions
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3 text-sm">
              <div className="p-3 bg-blue-50 rounded-lg">
                <p className="font-medium text-blue-900 mb-2">Test Logout:</p>
                <p className="text-blue-800">
                  Tests the standard logout functionality. Should clear user state, 
                  remove localStorage data, and redirect to login page.
                </p>
              </div>
              
              <div className="p-3 bg-yellow-50 rounded-lg">
                <p className="font-medium text-yellow-900 mb-2">Test Multiple Calls:</p>
                <p className="text-yellow-800">
                  Tests rapid multiple logout calls to ensure no race conditions 
                  or duplicate API calls occur.
                </p>
              </div>
              
              <div className="p-3 bg-green-50 rounded-lg">
                <p className="font-medium text-green-900 mb-2">Check State:</p>
                <p className="text-green-800">
                  Displays current authentication state, user data, and localStorage 
                  contents for debugging.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Expected Behavior */}
        <Card>
          <CardHeader>
            <CardTitle>Expected Logout Behavior</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <h4 className="font-medium text-green-900 mb-2">✅ Should Happen:</h4>
                <ul className="space-y-1 text-green-800">
                  <li>• User state cleared (user becomes null)</li>
                  <li>• isAuthenticated becomes false</li>
                  <li>• localStorage 'user' item removed</li>
                  <li>• Logout API called successfully</li>
                  <li>• Redirect to login page</li>
                  <li>• No console errors</li>
                </ul>
              </div>
              
              <div>
                <h4 className="font-medium text-red-900 mb-2">❌ Should NOT Happen:</h4>
                <ul className="space-y-1 text-red-800">
                  <li>• Multiple rapid API calls</li>
                  <li>• User data persisting after logout</li>
                  <li>• Access to protected routes</li>
                  <li>• JavaScript errors in console</li>
                  <li>• Infinite logout loops</li>
                  <li>• UI freezing or unresponsiveness</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
