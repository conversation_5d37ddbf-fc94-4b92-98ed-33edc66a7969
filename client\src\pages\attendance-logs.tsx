import { useState } from "react";
import { Head<PERSON> } from "@/components/layout/header";
import { Sidebar } from "@/components/layout/sidebar";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useQuery } from "@tanstack/react-query";
import { format, isToday, parseISO } from "date-fns";
import { Calendar, Search, Download, User, Filter } from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";

interface AttendanceRecord {
  id: number;
  userId: number;
  checkInTime: string;
  checkOutTime?: string;
  status: string;
  location: string;
  user: {
    fullName: string;
    department: string;
    employeeId: string;
  } | null;
}

export default function AttendanceLogs() {
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [dateFilter, setDateFilter] = useState("all");

  const { data: attendanceRecords, isLoading } = useQuery<AttendanceRecord[]>({
    queryKey: ["/api/attendance/recent"],
  });

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "present":
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-100">Present</Badge>;
      case "late":
        return <Badge className="bg-amber-100 text-amber-800 hover:bg-amber-100">Late</Badge>;
      case "absent":
        return <Badge className="bg-red-100 text-red-800 hover:bg-red-100">Absent</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const filteredRecords = attendanceRecords?.filter(record => {
    const matchesSearch = !searchTerm || 
      record.user?.fullName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      record.user?.employeeId?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      record.user?.department?.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus = statusFilter === "all" || record.status === statusFilter;

    let matchesDate = true;
    if (dateFilter === "today") {
      matchesDate = isToday(parseISO(record.checkInTime));
    }

    return matchesSearch && matchesStatus && matchesDate;
  }) || [];

  const calculateWorkingHours = (checkIn: string, checkOut?: string) => {
    if (!checkOut) return "--";
    
    const checkInTime = new Date(checkIn);
    const checkOutTime = new Date(checkOut);
    const diffMs = checkOutTime.getTime() - checkInTime.getTime();
    const diffHours = diffMs / (1000 * 60 * 60);
    
    return `${diffHours.toFixed(1)}h`;
  };

  return (
    <div className="min-h-screen flex flex-col bg-surface">
      <Header />
      
      <main className="flex-1 flex">
        <Sidebar />
        
        <div className="flex-1 p-6 lg:p-8">
          {/* Page Header */}
          <div className="flex justify-between items-center mb-8">
            <div>
              <h2 className="text-2xl font-medium text-gray-900 mb-2">Attendance Logs</h2>
              <p className="text-gray-600">View and manage employee attendance records</p>
            </div>
            
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Export Report
            </Button>
          </div>

          {/* Filters */}
          <Card className="mb-6">
            <CardContent className="pt-6">
              <div className="flex flex-col lg:flex-row lg:items-center lg:space-x-4 space-y-4 lg:space-y-0">
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Search by name, ID, or department..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-9"
                  />
                </div>
                
                <div className="flex space-x-2">
                  <Select value={statusFilter} onValueChange={setStatusFilter}>
                    <SelectTrigger className="w-40">
                      <Filter className="h-4 w-4 mr-2" />
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Status</SelectItem>
                      <SelectItem value="present">Present</SelectItem>
                      <SelectItem value="late">Late</SelectItem>
                      <SelectItem value="absent">Absent</SelectItem>
                    </SelectContent>
                  </Select>
                  
                  <Select value={dateFilter} onValueChange={setDateFilter}>
                    <SelectTrigger className="w-40">
                      <Calendar className="h-4 w-4 mr-2" />
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Dates</SelectItem>
                      <SelectItem value="today">Today</SelectItem>
                      <SelectItem value="thisWeek">This Week</SelectItem>
                      <SelectItem value="thisMonth">This Month</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Attendance Records */}
          <Card>
            <CardHeader>
              <CardTitle>Attendance Records ({filteredRecords.length})</CardTitle>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="space-y-4">
                  {[...Array(10)].map((_, i) => (
                    <Skeleton key={i} className="h-16 w-full" />
                  ))}
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b border-gray-200">
                        <th className="text-left py-3 px-2 text-sm font-medium text-gray-600">Employee</th>
                        <th className="text-left py-3 px-2 text-sm font-medium text-gray-600">Date</th>
                        <th className="text-left py-3 px-2 text-sm font-medium text-gray-600">Check In</th>
                        <th className="text-left py-3 px-2 text-sm font-medium text-gray-600">Check Out</th>
                        <th className="text-left py-3 px-2 text-sm font-medium text-gray-600">Working Hours</th>
                        <th className="text-left py-3 px-2 text-sm font-medium text-gray-600">Status</th>
                        <th className="text-left py-3 px-2 text-sm font-medium text-gray-600">Location</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-100">
                      {filteredRecords.map((record) => (
                        <tr key={record.id} className="hover:bg-gray-50">
                          <td className="py-4 px-2">
                            <div className="flex items-center space-x-3">
                              <div className="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center">
                                <User className="h-4 w-4 text-gray-600" />
                              </div>
                              <div>
                                <p className="text-sm font-medium text-gray-900">
                                  {record.user?.fullName || "Unknown"}
                                </p>
                                <p className="text-xs text-gray-500">
                                  {record.user?.employeeId} • {record.user?.department}
                                </p>
                              </div>
                            </div>
                          </td>
                          <td className="py-4 px-2 text-sm text-gray-900">
                            {format(parseISO(record.checkInTime), "MMM dd, yyyy")}
                          </td>
                          <td className="py-4 px-2 text-sm text-gray-900">
                            {format(parseISO(record.checkInTime), "hh:mm a")}
                          </td>
                          <td className="py-4 px-2 text-sm text-gray-900">
                            {record.checkOutTime 
                              ? format(parseISO(record.checkOutTime), "hh:mm a")
                              : "--"
                            }
                          </td>
                          <td className="py-4 px-2 text-sm text-gray-900">
                            {calculateWorkingHours(record.checkInTime, record.checkOutTime)}
                          </td>
                          <td className="py-4 px-2">
                            {getStatusBadge(record.status)}
                          </td>
                          <td className="py-4 px-2 text-sm text-gray-600">
                            {record.location}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                  
                  {filteredRecords.length === 0 && (
                    <div className="text-center py-8 text-gray-500">
                      {searchTerm || statusFilter !== "all" || dateFilter !== "all" 
                        ? "No attendance records found matching your filters"
                        : "No attendance records found"
                      }
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  );
}
