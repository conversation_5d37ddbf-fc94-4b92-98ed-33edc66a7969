import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/layout/header";
import { Sidebar } from "@/components/layout/sidebar";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { 
  Settings as SettingsIcon, 
  Camera, 
  Bell, 
  Shield, 
  Database,
  Clock,
  Users,
  Palette,
  Save,
  RefreshCw
} from "lucide-react";

export default function Settings() {
  const { toast } = useToast();
  const [settings, setSettings] = useState({
    // System Settings
    systemName: "AttendanceTracker Pro",
    workStartTime: "09:00",
    workEndTime: "17:00",
    lateThreshold: "15", // minutes
    
    // Recognition Settings
    recognitionConfidence: "80",
    emotionDetection: true,
    frownAlerts: true,
    autoCheckout: false,
    
    // Notification Settings
    emailNotifications: true,
    smsAlerts: false,
    dailyReports: true,
    weeklyReports: true,
    
    // Security Settings
    sessionTimeout: "480", // minutes (8 hours)
    requireStrongPasswords: true,
    twoFactorAuth: false,
    auditLogging: true,
    
    // UI Settings
    darkMode: false,
    compactView: false,
    showConfidence: true,
    animatedUI: true
  });

  const handleSettingChange = (key: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const saveSettings = () => {
    // In a real app, this would save to the backend
    toast({
      title: "Settings Saved",
      description: "Your configuration has been updated successfully.",
    });
  };

  const resetToDefaults = () => {
    // Reset to default values
    setSettings({
      systemName: "AttendanceTracker Pro",
      workStartTime: "09:00",
      workEndTime: "17:00",
      lateThreshold: "15",
      recognitionConfidence: "80",
      emotionDetection: true,
      frownAlerts: true,
      autoCheckout: false,
      emailNotifications: true,
      smsAlerts: false,
      dailyReports: true,
      weeklyReports: true,
      sessionTimeout: "480",
      requireStrongPasswords: true,
      twoFactorAuth: false,
      auditLogging: true,
      darkMode: false,
      compactView: false,
      showConfidence: true,
      animatedUI: true
    });
    
    toast({
      title: "Settings Reset",
      description: "All settings have been restored to default values.",
    });
  };

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-br from-slate-50 to-blue-50">
      <Header />
      
      <main className="flex-1 flex">
        <Sidebar />
        
        <div className="flex-1 p-6 lg:p-8">
          {/* Page Header */}
          <div className="mb-8">
            <div className="bg-white/60 backdrop-blur-sm rounded-2xl p-6 border border-white/20 shadow-lg">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="bg-gradient-to-r from-gray-500 to-gray-600 p-3 rounded-xl">
                    <SettingsIcon className="h-8 w-8 text-white" />
                  </div>
                  <div>
                    <h2 className="text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent">
                      System Settings
                    </h2>
                    <p className="text-gray-600 text-lg">Configure system preferences and behavior</p>
                  </div>
                </div>
                <div className="flex space-x-3">
                  <Button 
                    variant="outline"
                    onClick={resetToDefaults}
                  >
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Reset to Defaults
                  </Button>
                  <Button 
                    className="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700"
                    onClick={saveSettings}
                  >
                    <Save className="h-4 w-4 mr-2" />
                    Save Changes
                  </Button>
                </div>
              </div>
            </div>
          </div>

          {/* Settings Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            
            {/* System Configuration */}
            <Card className="bg-white/60 backdrop-blur-sm border border-white/20 shadow-lg">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Clock className="h-5 w-5 mr-2 text-blue-600" />
                  System Configuration
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-2">
                  <Label htmlFor="systemName">System Name</Label>
                  <Input
                    id="systemName"
                    value={settings.systemName}
                    onChange={(e) => handleSettingChange('systemName', e.target.value)}
                  />
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="workStartTime">Work Start Time</Label>
                    <Input
                      id="workStartTime"
                      type="time"
                      value={settings.workStartTime}
                      onChange={(e) => handleSettingChange('workStartTime', e.target.value)}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="workEndTime">Work End Time</Label>
                    <Input
                      id="workEndTime"
                      type="time"
                      value={settings.workEndTime}
                      onChange={(e) => handleSettingChange('workEndTime', e.target.value)}
                    />
                  </div>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="lateThreshold">Late Threshold (minutes)</Label>
                  <Input
                    id="lateThreshold"
                    type="number"
                    value={settings.lateThreshold}
                    onChange={(e) => handleSettingChange('lateThreshold', e.target.value)}
                  />
                  <p className="text-xs text-gray-500">
                    Employees arriving after this many minutes will be marked as late
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Recognition Settings */}
            <Card className="bg-white/60 backdrop-blur-sm border border-white/20 shadow-lg">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Camera className="h-5 w-5 mr-2 text-purple-600" />
                  Face Recognition
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-2">
                  <Label htmlFor="recognitionConfidence">Recognition Confidence Threshold (%)</Label>
                  <Input
                    id="recognitionConfidence"
                    type="number"
                    min="50"
                    max="100"
                    value={settings.recognitionConfidence}
                    onChange={(e) => handleSettingChange('recognitionConfidence', e.target.value)}
                  />
                  <p className="text-xs text-gray-500">
                    Minimum confidence level required for successful recognition
                  </p>
                </div>
                
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="emotionDetection">Emotion Detection</Label>
                      <p className="text-xs text-gray-500">Enable frown and mood recognition</p>
                    </div>
                    <Switch
                      id="emotionDetection"
                      checked={settings.emotionDetection}
                      onCheckedChange={(checked) => handleSettingChange('emotionDetection', checked)}
                    />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="frownAlerts">Frown Alerts</Label>
                      <p className="text-xs text-gray-500">Alert HR when sadness is detected</p>
                    </div>
                    <Switch
                      id="frownAlerts"
                      checked={settings.frownAlerts}
                      onCheckedChange={(checked) => handleSettingChange('frownAlerts', checked)}
                    />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="autoCheckout">Auto Check-out</Label>
                      <p className="text-xs text-gray-500">Automatically check out at end of day</p>
                    </div>
                    <Switch
                      id="autoCheckout"
                      checked={settings.autoCheckout}
                      onCheckedChange={(checked) => handleSettingChange('autoCheckout', checked)}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Notification Settings */}
            <Card className="bg-white/60 backdrop-blur-sm border border-white/20 shadow-lg">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Bell className="h-5 w-5 mr-2 text-yellow-600" />
                  Notifications
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="emailNotifications">Email Notifications</Label>
                    <p className="text-xs text-gray-500">Send attendance alerts via email</p>
                  </div>
                  <Switch
                    id="emailNotifications"
                    checked={settings.emailNotifications}
                    onCheckedChange={(checked) => handleSettingChange('emailNotifications', checked)}
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="smsAlerts">SMS Alerts</Label>
                    <p className="text-xs text-gray-500">Send urgent alerts via SMS</p>
                  </div>
                  <Switch
                    id="smsAlerts"
                    checked={settings.smsAlerts}
                    onCheckedChange={(checked) => handleSettingChange('smsAlerts', checked)}
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="dailyReports">Daily Reports</Label>
                    <p className="text-xs text-gray-500">Automatic daily attendance summaries</p>
                  </div>
                  <Switch
                    id="dailyReports"
                    checked={settings.dailyReports}
                    onCheckedChange={(checked) => handleSettingChange('dailyReports', checked)}
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="weeklyReports">Weekly Reports</Label>
                    <p className="text-xs text-gray-500">Weekly attendance analytics</p>
                  </div>
                  <Switch
                    id="weeklyReports"
                    checked={settings.weeklyReports}
                    onCheckedChange={(checked) => handleSettingChange('weeklyReports', checked)}
                  />
                </div>
              </CardContent>
            </Card>

            {/* Security Settings */}
            <Card className="bg-white/60 backdrop-blur-sm border border-white/20 shadow-lg">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Shield className="h-5 w-5 mr-2 text-red-600" />
                  Security & Privacy
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-2">
                  <Label htmlFor="sessionTimeout">Session Timeout (minutes)</Label>
                  <Input
                    id="sessionTimeout"
                    type="number"
                    min="30"
                    max="1440"
                    value={settings.sessionTimeout}
                    onChange={(e) => handleSettingChange('sessionTimeout', e.target.value)}
                  />
                  <p className="text-xs text-gray-500">
                    Automatically log out users after this period of inactivity
                  </p>
                </div>
                
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="requireStrongPasswords">Strong Passwords</Label>
                      <p className="text-xs text-gray-500">Enforce password complexity requirements</p>
                    </div>
                    <Switch
                      id="requireStrongPasswords"
                      checked={settings.requireStrongPasswords}
                      onCheckedChange={(checked) => handleSettingChange('requireStrongPasswords', checked)}
                    />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="twoFactorAuth">Two-Factor Authentication</Label>
                      <p className="text-xs text-gray-500">Require 2FA for admin accounts</p>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Switch
                        id="twoFactorAuth"
                        checked={settings.twoFactorAuth}
                        onCheckedChange={(checked) => handleSettingChange('twoFactorAuth', checked)}
                      />
                      <Badge variant="outline" className="text-xs">Coming Soon</Badge>
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="auditLogging">Audit Logging</Label>
                      <p className="text-xs text-gray-500">Log all system access and changes</p>
                    </div>
                    <Switch
                      id="auditLogging"
                      checked={settings.auditLogging}
                      onCheckedChange={(checked) => handleSettingChange('auditLogging', checked)}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* UI Preferences */}
            <Card className="bg-white/60 backdrop-blur-sm border border-white/20 shadow-lg lg:col-span-2">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Palette className="h-5 w-5 mr-2 text-indigo-600" />
                  User Interface
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <Label htmlFor="darkMode">Dark Mode</Label>
                        <p className="text-xs text-gray-500">Switch to dark theme</p>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Switch
                          id="darkMode"
                          checked={settings.darkMode}
                          onCheckedChange={(checked) => handleSettingChange('darkMode', checked)}
                        />
                        <Badge variant="outline" className="text-xs">Coming Soon</Badge>
                      </div>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div>
                        <Label htmlFor="compactView">Compact View</Label>
                        <p className="text-xs text-gray-500">Reduce spacing and padding</p>
                      </div>
                      <Switch
                        id="compactView"
                        checked={settings.compactView}
                        onCheckedChange={(checked) => handleSettingChange('compactView', checked)}
                      />
                    </div>
                  </div>
                  
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <Label htmlFor="showConfidence">Show Confidence Scores</Label>
                        <p className="text-xs text-gray-500">Display recognition confidence percentages</p>
                      </div>
                      <Switch
                        id="showConfidence"
                        checked={settings.showConfidence}
                        onCheckedChange={(checked) => handleSettingChange('showConfidence', checked)}
                      />
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div>
                        <Label htmlFor="animatedUI">Animated Interface</Label>
                        <p className="text-xs text-gray-500">Enable smooth transitions and animations</p>
                      </div>
                      <Switch
                        id="animatedUI"
                        checked={settings.animatedUI}
                        onCheckedChange={(checked) => handleSettingChange('animatedUI', checked)}
                      />
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* System Status */}
          <Card className="mt-8 bg-white/60 backdrop-blur-sm border border-white/20 shadow-lg">
            <CardHeader>
              <CardTitle className="flex items-center">
                <Database className="h-5 w-5 mr-2 text-green-600" />
                System Status
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div className="text-center p-4 bg-green-50 rounded-lg">
                  <div className="w-3 h-3 bg-green-500 rounded-full mx-auto mb-2"></div>
                  <p className="text-sm font-medium text-green-900">Database</p>
                  <p className="text-xs text-green-700">Connected</p>
                </div>
                <div className="text-center p-4 bg-green-50 rounded-lg">
                  <div className="w-3 h-3 bg-green-500 rounded-full mx-auto mb-2"></div>
                  <p className="text-sm font-medium text-green-900">Face Recognition</p>
                  <p className="text-xs text-green-700">Online</p>
                </div>
                <div className="text-center p-4 bg-green-50 rounded-lg">
                  <div className="w-3 h-3 bg-green-500 rounded-full mx-auto mb-2"></div>
                  <p className="text-sm font-medium text-green-900">Notifications</p>
                  <p className="text-xs text-green-700">Active</p>
                </div>
                <div className="text-center p-4 bg-blue-50 rounded-lg">
                  <div className="w-3 h-3 bg-blue-500 rounded-full mx-auto mb-2"></div>
                  <p className="text-sm font-medium text-blue-900">Last Backup</p>
                  <p className="text-xs text-blue-700">2 hours ago</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  );
}
