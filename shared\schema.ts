import { pgTable, text, serial, integer, boolean, timestamp } from "drizzle-orm/pg-core";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";

export const users = pgTable("users", {
  id: serial("id").primaryKey(),
  username: text("username").notNull().unique(),
  password: text("password").notNull(),
  fullName: text("full_name").notNull(),
  role: text("role").notNull(), // 'admin' | 'student'
  department: text("department"),
  employeeId: text("employee_id").unique(),
  profileImage: text("profile_image"),
  faceDescriptor: text("face_descriptor"), // JSON string of face embedding
  faceEnrolled: boolean("face_enrolled").default(false),
  enrollmentDate: timestamp("enrollment_date"),
  isActive: boolean("is_active").default(true),
});

export const attendanceRecords = pgTable("attendance_records", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").notNull(),
  checkInTime: timestamp("check_in_time").notNull(),
  checkOutTime: timestamp("check_out_time"),
  status: text("status").notNull(), // 'present', 'late', 'absent'
  location: text("location").notNull(),
  recognitionConfidence: integer("recognition_confidence"), // 0-100
});

export const insertUserSchema = createInsertSchema(users).omit({
  id: true,
  isActive: true,
});

export const insertAttendanceSchema = createInsertSchema(attendanceRecords).omit({
  id: true,
});

export const loginSchema = z.object({
  username: z.string().min(1, "Username is required"),
  password: z.string().min(1, "Password is required"),
});

export const faceRecognitionSchema = z.object({
  imageData: z.string().min(1, "Image data is required"),
});

export const faceEnrollmentSchema = z.object({
  userId: z.number().min(1, "User ID is required"),
  imageData: z.string().min(1, "Image data is required"),
});

export const imageQualitySchema = z.object({
  imageData: z.string().min(1, "Image data is required"),
});

export type InsertUser = z.infer<typeof insertUserSchema>;
export type User = typeof users.$inferSelect;
export type InsertAttendance = z.infer<typeof insertAttendanceSchema>;
export type AttendanceRecord = typeof attendanceRecords.$inferSelect;
export type LoginCredentials = z.infer<typeof loginSchema>;
export type FaceRecognitionData = z.infer<typeof faceRecognitionSchema>;
export type FaceEnrollmentData = z.infer<typeof faceEnrollmentSchema>;
export type ImageQualityData = z.infer<typeof imageQualitySchema>;
