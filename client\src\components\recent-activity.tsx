import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useQuery } from "@tanstack/react-query";
import { ArrowRight, User } from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";
import { format } from "date-fns";
import { useLocation } from "wouter";

interface AttendanceRecord {
  id: number;
  userId: number;
  checkInTime: string;
  status: string;
  location: string;
  user: {
    fullName: string;
    department: string;
    employeeId: string;
  } | null;
}

export function RecentActivity() {
  const [, setLocation] = useLocation();
  const { data: recentRecords, isLoading } = useQuery<AttendanceRecord[]>({
    queryKey: ["/api/attendance/recent"],
  });

  const handleViewAll = () => {
    console.log("🔗 Navigating to Attendance Logs");
    setLocation("/attendance");
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <Skeleton className="h-6 w-48" />
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[...Array(5)].map((_, i) => (
              <Skeleton key={i} className="h-16 w-full" />
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "present":
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-100">Check In</Badge>;
      case "late":
        return <Badge className="bg-amber-100 text-amber-800 hover:bg-amber-100">Late Check In</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle className="text-lg font-medium">Recent Check-ins</CardTitle>
        <Button
          variant="ghost"
          size="sm"
          onClick={handleViewAll}
          className="text-primary hover:text-primary/80 transition-colors duration-200"
        >
          View All
          <ArrowRight className="h-4 w-4 ml-1" />
        </Button>
      </CardHeader>

      <CardContent>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-gray-200">
                <th className="text-left py-3 px-2 text-sm font-medium text-gray-600">Employee</th>
                <th className="text-left py-3 px-2 text-sm font-medium text-gray-600">Time</th>
                <th className="text-left py-3 px-2 text-sm font-medium text-gray-600">Status</th>
                <th className="text-left py-3 px-2 text-sm font-medium text-gray-600">Location</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-100">
              {recentRecords?.map((record) => (
                <tr key={record.id} className="hover:bg-gray-50">
                  <td className="py-4 px-2">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center">
                        <User className="h-4 w-4 text-gray-600" />
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-900">
                          {record.user?.fullName || "Unknown"}
                        </p>
                        <p className="text-xs text-gray-500">
                          {record.user?.department || "N/A"}
                        </p>
                      </div>
                    </div>
                  </td>
                  <td className="py-4 px-2 text-sm text-gray-900">
                    {format(new Date(record.checkInTime), "hh:mm a")}
                  </td>
                  <td className="py-4 px-2">
                    {getStatusBadge(record.status)}
                  </td>
                  <td className="py-4 px-2 text-sm text-gray-600">
                    {record.location}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
          
          {(!recentRecords || recentRecords.length === 0) && (
            <div className="text-center py-8 text-gray-500">
              No recent check-ins found
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
