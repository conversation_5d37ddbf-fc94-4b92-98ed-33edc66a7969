import { useState, useRef, useCallback, useEffect } from 'react';
import { detectBrowserSupport, getOptimalConstraints, getCameraErrorMessage, canAccessCamera } from '@/lib/camera-utils';

export interface CameraError {
  type: 'permission' | 'not_found' | 'not_supported' | 'unknown';
  message: string;
}

export interface CameraState {
  isLoading: boolean;
  isActive: boolean;
  error: CameraError | null;
  stream: MediaStream | null;
}

export interface CameraCapabilities {
  hasCamera: boolean;
  hasPermission: boolean | null;
  supportedConstraints: MediaTrackSupportedConstraints | null;
}

export function useCamera() {
  const [state, setState] = useState<CameraState>({
    isLoading: false,
    isActive: false,
    error: null,
    stream: null,
  });

  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const streamRef = useRef<MediaStream | null>(null);

  // Check camera capabilities
  const checkCapabilities = useCallback(async (): Promise<CameraCapabilities> => {
    const support = detectBrowserSupport();
    const accessCheck = canAccessCamera();

    if (!accessCheck.supported) {
      return {
        hasCamera: false,
        hasPermission: false,
        supportedConstraints: null,
      };
    }

    try {
      const devices = await navigator.mediaDevices.enumerateDevices();
      const hasCamera = devices.some(device => device.kind === 'videoinput');

      // Check permissions
      let hasPermission: boolean | null = null;
      if (support.hasPermissionsAPI) {
        try {
          const permissionStatus = await navigator.permissions.query({ name: 'camera' as PermissionName });
          hasPermission = permissionStatus.state === 'granted';
        } catch (e) {
          hasPermission = null;
        }
      }

      const supportedConstraints = navigator.mediaDevices.getSupportedConstraints();

      return {
        hasCamera,
        hasPermission,
        supportedConstraints,
      };
    } catch (error) {
      return {
        hasCamera: false,
        hasPermission: false,
        supportedConstraints: null,
      };
    }
  }, []);

  // Start camera
  const startCamera = useCallback(async (constraints?: MediaStreamConstraints) => {
    setState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      // Check if camera access is supported
      const accessCheck = canAccessCamera();
      if (!accessCheck.supported) {
        throw new Error(accessCheck.reason || 'Camera access not supported');
      }

      // Use optimal constraints if none provided
      const finalConstraints = constraints || getOptimalConstraints();

      const stream = await navigator.mediaDevices.getUserMedia(finalConstraints);

      streamRef.current = stream;

      if (videoRef.current) {
        videoRef.current.srcObject = stream;
        await new Promise<void>((resolve, reject) => {
          if (videoRef.current) {
            videoRef.current.onloadedmetadata = () => resolve();
            videoRef.current.onerror = () => reject(new Error('Video load failed'));
          }
        });
      }

      setState(prev => ({
        ...prev,
        isLoading: false,
        isActive: true,
        stream,
        error: null,
      }));

    } catch (error: any) {
      console.error('Camera access error:', error);

      const errorInfo = getCameraErrorMessage(error);
      const cameraError: CameraError = {
        type: errorInfo.type as any,
        message: `${errorInfo.message}. ${errorInfo.suggestion}`,
      };

      setState(prev => ({
        ...prev,
        isLoading: false,
        isActive: false,
        error: cameraError,
        stream: null,
      }));
    }
  }, []);

  // Stop camera
  const stopCamera = useCallback(() => {
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => {
        track.stop();
      });
      streamRef.current = null;
    }

    if (videoRef.current) {
      videoRef.current.srcObject = null;
    }

    setState(prev => ({
      ...prev,
      isActive: false,
      stream: null,
      error: null,
    }));
  }, []);

  // Capture image
  const captureImage = useCallback((): Promise<string> => {
    return new Promise((resolve, reject) => {
      if (!videoRef.current || !state.isActive) {
        reject(new Error('Camera not active'));
        return;
      }

      if (!canvasRef.current) {
        reject(new Error('Canvas not available'));
        return;
      }

      const video = videoRef.current;
      const canvas = canvasRef.current;
      const context = canvas.getContext('2d');

      if (!context) {
        reject(new Error('Canvas context not available'));
        return;
      }

      // Set canvas dimensions to match video
      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;

      // Draw current video frame to canvas
      context.drawImage(video, 0, 0, canvas.width, canvas.height);

      // Convert to base64
      try {
        const imageData = canvas.toDataURL('image/jpeg', 0.8);
        resolve(imageData);
      } catch (error) {
        reject(new Error('Failed to capture image'));
      }
    });
  }, [state.isActive]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      stopCamera();
    };
  }, [stopCamera]);

  return {
    // State
    isLoading: state.isLoading,
    isActive: state.isActive,
    error: state.error,
    stream: state.stream,
    
    // Refs for components
    videoRef,
    canvasRef,
    
    // Methods
    checkCapabilities,
    startCamera,
    stopCamera,
    captureImage,
  };
}
