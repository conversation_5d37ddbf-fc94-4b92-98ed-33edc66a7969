import { createCanvas, loadImage } from 'canvas';
import fs from 'fs';

async function resizeImage() {
  try {
    // Load the original image
    const image = await loadImage('test-real-face.jpg');
    
    // Calculate new dimensions (max 400px width/height)
    const maxSize = 400;
    let { width, height } = image;
    
    if (width > maxSize || height > maxSize) {
      const ratio = Math.min(maxSize / width, maxSize / height);
      width = Math.floor(width * ratio);
      height = Math.floor(height * ratio);
    }
    
    console.log(`📏 Resizing image to ${width}x${height}`);
    
    // Create canvas and resize
    const canvas = createCanvas(width, height);
    const ctx = canvas.getContext('2d');
    ctx.drawImage(image, 0, 0, width, height);
    
    // Save resized image
    const buffer = canvas.toBuffer('image/jpeg', { quality: 0.8 });
    fs.writeFileSync('test-real-face-small.jpg', buffer);
    
    console.log(`✅ Resized image saved: ${buffer.length} bytes`);
    
    // Convert to base64 for testing
    const base64 = buffer.toString('base64');
    console.log(`📊 Base64 size: ${base64.length} characters`);
    
    return base64;
  } catch (error) {
    console.error('❌ Error resizing image:', error);
    return null;
  }
}

resizeImage();
