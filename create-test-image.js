import { createCanvas } from 'canvas';
import fs from 'fs';

// Create a simple test face image (200x200 pixels)
function createTestFaceImage() {
  const canvas = createCanvas(200, 200);
  const ctx = canvas.getContext('2d');
  
  // Fill background
  ctx.fillStyle = '#f0f0f0';
  ctx.fillRect(0, 0, 200, 200);
  
  // Draw face outline (circle)
  ctx.fillStyle = '#ffdbac';
  ctx.beginPath();
  ctx.arc(100, 100, 80, 0, 2 * Math.PI);
  ctx.fill();
  
  // Draw eyes
  ctx.fillStyle = '#000';
  ctx.beginPath();
  ctx.arc(80, 80, 8, 0, 2 * Math.PI);
  ctx.fill();
  
  ctx.beginPath();
  ctx.arc(120, 80, 8, 0, 2 * Math.PI);
  ctx.fill();
  
  // Draw nose
  ctx.strokeStyle = '#000';
  ctx.lineWidth = 2;
  ctx.beginPath();
  ctx.moveTo(100, 90);
  ctx.lineTo(95, 110);
  ctx.lineTo(105, 110);
  ctx.stroke();
  
  // Draw mouth
  ctx.beginPath();
  ctx.arc(100, 130, 15, 0, Math.PI);
  ctx.stroke();
  
  return canvas.toDataURL('image/png');
}

// Create test image
const testImageBase64 = createTestFaceImage();

// Save to file for reference
const base64Data = testImageBase64.replace(/^data:image\/png;base64,/, '');
fs.writeFileSync('test-face.png', Buffer.from(base64Data, 'base64'));

console.log('✅ Test face image created: test-face.png');
console.log('📏 Image size: 200x200 pixels');
console.log('🔗 Base64 length:', testImageBase64.length);

// Export for use in tests
export { testImageBase64 };
