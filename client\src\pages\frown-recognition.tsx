import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/layout/header";
import { Sidebar } from "@/components/layout/sidebar";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useAuth } from "@/hooks/use-auth";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { CameraInterface } from "@/components/camera-interface";
import { Camera, Upload, Scan, CheckCircle, Sparkles, Frown, Smile, AlertTriangle } from "lucide-react";

interface FrownRecognitionResult {
  recognized: boolean;
  user?: {
    id: number;
    fullName: string;
    employeeId: string;
    department: string;
  };
  confidence?: number;
  emotion?: "happy" | "neutral" | "frown" | "sad";
  emotionConfidence?: number;
}

export default function FrownRecognition() {
  const [recognitionResult, setRecognitionResult] = useState<FrownRecognitionResult | null>(null);
  const { user, isAdmin } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const recognitionMutation = useMutation({
    mutationFn: async (imageData: string) => {
      const response = await apiRequest("POST", "/api/frown-recognition", { imageData });
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `HTTP ${response.status}`);
      }
      return await response.json();
    },
    onSuccess: (data) => {
      console.log("Frown recognition result:", data);
      setRecognitionResult(data);
      if (data.recognized) {
        const emotionMessage = data.emotion === "frown" || data.emotion === "sad"
          ? "We detected you might be having a tough day. Would you like to speak with someone?"
          : "Great to see you in good spirits!";

        toast({
          title: "Face & Emotion Recognized",
          description: `${data.user.fullName} - ${emotionMessage}`,
        });
      } else {
        toast({
          title: "Recognition Failed",
          description: "Face not recognized. Please try again.",
          variant: "destructive",
        });
      }
    },
    onError: (error: any) => {
      console.error("Frown recognition error:", error);
      toast({
        title: "Recognition Error",
        description: error.message || "Failed to process face and emotion recognition.",
        variant: "destructive",
      });
    },
  });

  const checkInMutation = useMutation({
    mutationFn: async (userId: number) => {
      const response = await apiRequest("POST", "/api/attendance/checkin", { userId });
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `HTTP ${response.status}`);
      }
      return await response.json();
    },
    onSuccess: () => {
      toast({
        title: "Check-in Successful",
        description: "Your attendance has been recorded with emotion analysis.",
      });
      queryClient.invalidateQueries({ queryKey: ["/api/attendance/stats"] });
      queryClient.invalidateQueries({ queryKey: ["/api/attendance/recent"] });
      setRecognitionResult(null);
    },
    onError: (error: any) => {
      console.error("Frown check-in error:", error);
      toast({
        title: "Check-in Failed",
        description: error.message || "Failed to record attendance.",
        variant: "destructive",
      });
    },
  });

  const handleCameraCapture = (imageData: string) => {
    console.log("Frown recognition camera captured image, starting analysis...");
    recognitionMutation.mutate(imageData);
  };

  const handleUpload = () => {
    // Create file input for image upload
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = 'image/*';
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        const reader = new FileReader();
        reader.onload = (event) => {
          const imageData = event.target?.result as string;
          recognitionMutation.mutate(imageData);
        };
        reader.readAsDataURL(file);
      }
    };
    input.click();
  };

  const handleCheckIn = () => {
    if (recognitionResult?.user) {
      checkInMutation.mutate(recognitionResult.user.id);
    }
  };

  const getEmotionIcon = (emotion?: string) => {
    switch (emotion) {
      case "happy":
        return <Smile className="h-5 w-5 text-green-600" />;
      case "frown":
      case "sad":
        return <Frown className="h-5 w-5 text-red-600" />;
      default:
        return <Scan className="h-5 w-5 text-blue-600" />;
    }
  };

  const getEmotionColor = (emotion?: string) => {
    switch (emotion) {
      case "happy":
        return "text-green-600";
      case "frown":
      case "sad":
        return "text-red-600";
      default:
        return "text-blue-600";
    }
  };

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-br from-slate-50 to-blue-50">
      <Header />
      
      <main className="flex-1 flex">
        <Sidebar />
        
        <div className="flex-1 p-6 lg:p-8">
          {/* Page Header */}
          <div className="mb-8">
            <div className="bg-white/60 backdrop-blur-sm rounded-2xl p-6 border border-white/20 shadow-lg">
              <div className="flex items-center space-x-4 mb-4">
                <div className="bg-gradient-to-r from-purple-500 to-pink-600 p-3 rounded-xl">
                  <Frown className="h-8 w-8 text-white" />
                </div>
                <div>
                  <h2 className="text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent">
                    Frown Recognition System
                  </h2>
                  <p className="text-gray-600 text-lg">Advanced emotion detection with facial recognition</p>
                </div>
              </div>
              <div className="flex items-center space-x-4 text-sm text-gray-500">
                <span className="flex items-center">
                  <div className="w-2 h-2 bg-purple-500 rounded-full mr-2"></div>
                  Emotion AI Active
                </span>
                <span>|</span>
                <span>Real-time Mood Analysis</span>
                <span>|</span>
                <span>{new Date().toLocaleDateString('en-US', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' })}</span>
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="grid grid-cols-1 xl:grid-cols-2 gap-8">
            
            {/* Frown Recognition Panel */}
            <div>
              <Card className="bg-white/60 backdrop-blur-sm border border-white/20 shadow-lg">
                <CardHeader className="flex flex-row items-center justify-between pb-4">
                  <CardTitle className="text-lg font-semibold flex items-center">
                    <div className="bg-gradient-to-r from-purple-500 to-pink-600 p-2 rounded-lg mr-3">
                      <Frown className="h-5 w-5 text-white" />
                    </div>
                    Emotion Recognition Check-in
                  </CardTitle>
                  <div className="flex items-center space-x-1 text-xs text-purple-600 bg-purple-50 px-2 py-1 rounded-full">
                    <div className="w-2 h-2 bg-purple-500 rounded-full animate-pulse"></div>
                    <span>AI Active</span>
                  </div>
                </CardHeader>
                
                <CardContent className="space-y-6">
                  {/* Camera Interface */}
                  <CameraInterface
                    onCapture={handleCameraCapture}
                    isProcessing={recognitionMutation.isPending}
                    variant="emotion"
                  />

                  {/* Upload Button */}
                  <div className="flex justify-center">
                    <Button
                      variant="outline"
                      onClick={handleUpload}
                      disabled={recognitionMutation.isPending}
                      className="flex items-center space-x-2"
                    >
                      <Upload className="h-4 w-4" />
                      <span>Upload Photo</span>
                    </Button>
                  </div>

                  {/* Recognition Status */}
                  {recognitionResult && (
                    <div className="space-y-4">
                      {recognitionResult.recognized && recognitionResult.user ? (
                        <div className="bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-xl p-4">
                          <div className="flex items-center space-x-3">
                            <div className="bg-green-500 p-2 rounded-full">
                              <CheckCircle className="h-5 w-5 text-white" />
                            </div>
                            <div className="flex-1">
                              <p className="text-sm font-semibold text-green-900">Face Recognized Successfully</p>
                              <p className="text-xs text-green-700">
                                {recognitionResult.user.fullName} - ID: {recognitionResult.user.employeeId}
                              </p>
                              <p className="text-xs text-green-600 mt-1">
                                Recognition Confidence: {recognitionResult.confidence}%
                              </p>
                            </div>
                          </div>
                          
                          {/* Emotion Analysis */}
                          {recognitionResult.emotion && (
                            <div className="mt-3 pt-3 border-t border-green-200">
                              <div className="flex items-center space-x-3">
                                <div className="bg-white p-2 rounded-full">
                                  {getEmotionIcon(recognitionResult.emotion)}
                                </div>
                                <div>
                                  <p className="text-sm font-medium text-green-900">
                                    Emotion: <span className={`capitalize ${getEmotionColor(recognitionResult.emotion)}`}>
                                      {recognitionResult.emotion}
                                    </span>
                                  </p>
                                  <p className="text-xs text-green-600">
                                    Emotion Confidence: {recognitionResult.emotionConfidence}%
                                  </p>
                                </div>
                              </div>
                              
                              {(recognitionResult.emotion === "frown" || recognitionResult.emotion === "sad") && (
                                <div className="mt-3 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                                  <div className="flex items-center space-x-2">
                                    <AlertTriangle className="h-4 w-4 text-yellow-600" />
                                    <p className="text-sm text-yellow-800">
                                      We noticed you might be having a difficult day. Consider reaching out to HR or a colleague for support.
                                    </p>
                                  </div>
                                </div>
                              )}
                            </div>
                          )}
                        </div>
                      ) : (
                        <div className="bg-gradient-to-r from-red-50 to-rose-50 border border-red-200 rounded-xl p-4">
                          <div className="flex items-center space-x-3">
                            <div className="bg-red-500 p-2 rounded-full">
                              <Scan className="h-5 w-5 text-white" />
                            </div>
                            <div className="text-sm text-red-800 font-medium">Face not recognized. Please try again.</div>
                          </div>
                        </div>
                      )}
                      
                      {recognitionResult.recognized && recognitionResult.user && (
                        <Button
                          className="w-full h-12 bg-gradient-to-r from-purple-500 to-pink-600 hover:from-purple-600 hover:to-pink-700 text-white font-medium shadow-lg"
                          onClick={handleCheckIn}
                          disabled={checkInMutation.isPending}
                        >
                          {checkInMutation.isPending ? (
                            <div className="flex items-center space-x-2">
                              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                              <span>Recording Check-in...</span>
                            </div>
                          ) : (
                            <>
                              <CheckCircle className="h-5 w-5 mr-2" />
                              Complete Emotion-Aware Check-in
                            </>
                          )}
                        </Button>
                      )}
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>

            {/* Information Panel */}
            <div className="space-y-6">
              <Card className="bg-white/60 backdrop-blur-sm border border-white/20 shadow-lg">
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Sparkles className="h-5 w-5 mr-2 text-purple-600" />
                    How Frown Recognition Works
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-3">
                    <div className="flex items-start space-x-3">
                      <div className="bg-purple-100 p-2 rounded-lg">
                        <Camera className="h-4 w-4 text-purple-600" />
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-900">Facial Capture</p>
                        <p className="text-xs text-gray-600">Advanced camera system captures facial features</p>
                      </div>
                    </div>
                    <div className="flex items-start space-x-3">
                      <div className="bg-blue-100 p-2 rounded-lg">
                        <Scan className="h-4 w-4 text-blue-600" />
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-900">AI Analysis</p>
                        <p className="text-xs text-gray-600">Machine learning identifies facial expressions</p>
                      </div>
                    </div>
                    <div className="flex items-start space-x-3">
                      <div className="bg-green-100 p-2 rounded-lg">
                        <CheckCircle className="h-4 w-4 text-green-600" />
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-900">Emotion Detection</p>
                        <p className="text-xs text-gray-600">Recognizes happiness, neutrality, frowns, and sadness</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-white/60 backdrop-blur-sm border border-white/20 shadow-lg">
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <AlertTriangle className="h-5 w-5 mr-2 text-yellow-600" />
                    Wellness Support
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-gray-700 mb-3">
                    Our emotion recognition system helps identify when team members might need support.
                  </p>
                  <div className="space-y-2">
                    <Badge variant="outline" className="text-green-600 border-green-200">
                      <Smile className="h-3 w-3 mr-1" />
                      Happy: Great mood detected
                    </Badge>
                    <Badge variant="outline" className="text-blue-600 border-blue-200">
                      <Scan className="h-3 w-3 mr-1" />
                      Neutral: Normal expression
                    </Badge>
                    <Badge variant="outline" className="text-red-600 border-red-200">
                      <Frown className="h-3 w-3 mr-1" />
                      Frown/Sad: May need support
                    </Badge>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
