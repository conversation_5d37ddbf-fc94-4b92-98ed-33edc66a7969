import http from 'http';
import fs from 'fs';

// Read the resized real face image
const realFaceImageBase64 = fs.readFileSync('test-real-face-small.jpg', 'base64');
const realFaceImageDataURL = `data:image/jpeg;base64,${realFaceImageBase64}`;

console.log('🧪 Testing with Real Face Image');
console.log(`📏 Image size: ${realFaceImageBase64.length} bytes\n`);

// Helper function to make HTTP requests
async function makeRequest(path, method = 'GET', data = null) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 5000,
      path,
      method,
      headers: {
        'Content-Type': 'application/json',
      }
    };

    if (data) {
      const jsonData = JSON.stringify(data);
      options.headers['Content-Length'] = jsonData.length;
    }

    const req = http.request(options, (res) => {
      let responseData = '';
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      res.on('end', () => {
        try {
          const parsedData = responseData ? JSON.parse(responseData) : {};
          resolve({
            statusCode: res.statusCode,
            data: parsedData,
            raw: responseData
          });
        } catch (e) {
          resolve({
            statusCode: res.statusCode,
            data: responseData,
            raw: responseData
          });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }
    req.end();
  });
}

async function runRealFaceTests() {
  console.log('🔍 1. Testing Image Quality with Real Face...');
  try {
    const qualityResponse = await makeRequest('/api/image-quality', 'POST', {
      imageData: realFaceImageDataURL
    });
    console.log(`   Status: ${qualityResponse.statusCode}`);
    console.log(`   Valid: ${qualityResponse.data.isValid}`);
    console.log(`   Score: ${qualityResponse.data.score}`);
    console.log(`   Issues: ${qualityResponse.data.issues?.join(', ') || 'None'}`);
  } catch (error) {
    console.log(`   ❌ Error: ${error.message}`);
  }

  console.log('\n🔍 2. Testing Multiple Face Detection...');
  try {
    const detectResponse = await makeRequest('/api/detect-faces', 'POST', {
      imageData: realFaceImageDataURL
    });
    console.log(`   Status: ${detectResponse.statusCode}`);
    console.log(`   Face Count: ${detectResponse.data.faceCount}`);
    if (detectResponse.data.faces && detectResponse.data.faces.length > 0) {
      detectResponse.data.faces.forEach((face, index) => {
        console.log(`   Face ${index + 1}: Confidence ${face.confidence.toFixed(3)}`);
      });
    }
  } catch (error) {
    console.log(`   ❌ Error: ${error.message}`);
  }

  console.log('\n🔍 3. Testing Face Enrollment with Real Face...');
  try {
    const enrollResponse = await makeRequest('/api/face-enrollment', 'POST', {
      userId: 2,
      imageData: realFaceImageDataURL
    });
    console.log(`   Status: ${enrollResponse.statusCode}`);
    console.log(`   Success: ${enrollResponse.data.success}`);
    console.log(`   Message: ${enrollResponse.data.message}`);
    if (enrollResponse.data.qualityScore) {
      console.log(`   Quality Score: ${enrollResponse.data.qualityScore}`);
    }
  } catch (error) {
    console.log(`   ❌ Error: ${error.message}`);
  }

  // Wait for enrollment to process
  console.log('\n⏳ Waiting for enrollment to process...');
  await new Promise(resolve => setTimeout(resolve, 2000));

  console.log('\n🔍 4. Testing Face Recognition after Enrollment...');
  try {
    const recognitionResponse = await makeRequest('/api/face-recognition', 'POST', {
      imageData: realFaceImageDataURL
    });
    console.log(`   Status: ${recognitionResponse.statusCode}`);
    console.log(`   Recognized: ${recognitionResponse.data.recognized}`);
    if (recognitionResponse.data.user) {
      console.log(`   User: ${recognitionResponse.data.user.fullName} (${recognitionResponse.data.user.employeeId})`);
      console.log(`   Confidence: ${recognitionResponse.data.confidence}%`);
    } else {
      console.log(`   Message: ${recognitionResponse.data.message}`);
    }
  } catch (error) {
    console.log(`   ❌ Error: ${error.message}`);
  }

  console.log('\n🔍 5. Testing Emotion Recognition...');
  try {
    const emotionResponse = await makeRequest('/api/frown-recognition', 'POST', {
      imageData: realFaceImageDataURL
    });
    console.log(`   Status: ${emotionResponse.statusCode}`);
    console.log(`   Recognized: ${emotionResponse.data.recognized}`);
    if (emotionResponse.data.emotion) {
      console.log(`   Emotion: ${emotionResponse.data.emotion} (${emotionResponse.data.emotionConfidence}%)`);
    }
    if (emotionResponse.data.user) {
      console.log(`   User: ${emotionResponse.data.user.fullName}`);
    } else {
      console.log(`   Message: ${emotionResponse.data.message}`);
    }
  } catch (error) {
    console.log(`   ❌ Error: ${error.message}`);
  }

  console.log('\n🔍 6. Testing Attendance Check-in...');
  try {
    const checkinResponse = await makeRequest('/api/attendance/checkin', 'POST', {
      userId: 2,
      location: "Test Location"
    });
    console.log(`   Status: ${checkinResponse.statusCode}`);
    if (checkinResponse.statusCode === 200) {
      console.log(`   ✅ Check-in successful`);
      console.log(`   Status: ${checkinResponse.data.status}`);
      console.log(`   Location: ${checkinResponse.data.location}`);
    } else {
      console.log(`   Message: ${checkinResponse.data.message}`);
    }
  } catch (error) {
    console.log(`   ❌ Error: ${error.message}`);
  }

  console.log('\n📊 Real Face Test Complete!');
}

runRealFaceTests().catch(console.error);
