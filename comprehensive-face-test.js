import http from 'http';
import fs from 'fs';

// Read the test image
const testImageBase64 = fs.readFileSync('test-face.png', 'base64');
const testImageDataURL = `data:image/png;base64,${testImageBase64}`;

console.log('🧪 Starting Comprehensive Face Recognition System Test\n');
console.log('📊 Test Configuration:');
console.log(`   - Server: http://localhost:5000`);
console.log(`   - Test Image Size: ${testImageBase64.length} bytes`);
console.log(`   - Image Format: PNG (200x200)\n`);

// Helper function to make HTTP requests
async function makeRequest(path, method = 'GET', data = null) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 5000,
      path,
      method,
      headers: {
        'Content-Type': 'application/json',
      }
    };

    if (data) {
      const jsonData = JSON.stringify(data);
      options.headers['Content-Length'] = jsonData.length;
    }

    const req = http.request(options, (res) => {
      let responseData = '';
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      res.on('end', () => {
        try {
          const parsedData = responseData ? JSON.parse(responseData) : {};
          resolve({
            statusCode: res.statusCode,
            data: parsedData,
            raw: responseData
          });
        } catch (e) {
          resolve({
            statusCode: res.statusCode,
            data: responseData,
            raw: responseData
          });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }
    req.end();
  });
}

// Test functions
async function testBasicAPI() {
  console.log('🔍 1. Testing Basic API Endpoints...');
  
  try {
    // Test users endpoint
    const usersResponse = await makeRequest('/api/users');
    console.log(`   ✅ GET /api/users: ${usersResponse.statusCode} - ${usersResponse.data.length} users found`);
    
    // Test attendance stats
    const statsResponse = await makeRequest('/api/attendance/stats');
    console.log(`   ✅ GET /api/attendance/stats: ${statsResponse.statusCode} - Total: ${statsResponse.data.total}`);
    
    return { users: usersResponse.data, stats: statsResponse.data };
  } catch (error) {
    console.log(`   ❌ Basic API test failed: ${error.message}`);
    return null;
  }
}

async function testImageQuality() {
  console.log('\n🔍 2. Testing Image Quality Validation...');
  
  try {
    const response = await makeRequest('/api/image-quality', 'POST', {
      imageData: testImageDataURL
    });
    
    console.log(`   Status: ${response.statusCode}`);
    console.log(`   Valid: ${response.data.isValid}`);
    console.log(`   Score: ${response.data.score}`);
    console.log(`   Issues: ${response.data.issues?.join(', ') || 'None'}`);
    
    return response.data;
  } catch (error) {
    console.log(`   ❌ Image quality test failed: ${error.message}`);
    return null;
  }
}

async function testFaceRecognition() {
  console.log('\n🔍 3. Testing Face Recognition...');
  
  try {
    const response = await makeRequest('/api/face-recognition', 'POST', {
      imageData: testImageDataURL
    });
    
    console.log(`   Status: ${response.statusCode}`);
    console.log(`   Recognized: ${response.data.recognized}`);
    if (response.data.user) {
      console.log(`   User: ${response.data.user.fullName} (${response.data.user.employeeId})`);
      console.log(`   Confidence: ${response.data.confidence}%`);
    } else {
      console.log(`   Message: ${response.data.message}`);
    }
    
    return response.data;
  } catch (error) {
    console.log(`   ❌ Face recognition test failed: ${error.message}`);
    return null;
  }
}

async function testEmotionRecognition() {
  console.log('\n🔍 4. Testing Emotion Recognition...');
  
  try {
    const response = await makeRequest('/api/frown-recognition', 'POST', {
      imageData: testImageDataURL
    });
    
    console.log(`   Status: ${response.statusCode}`);
    console.log(`   Recognized: ${response.data.recognized}`);
    if (response.data.emotion) {
      console.log(`   Emotion: ${response.data.emotion} (${response.data.emotionConfidence}%)`);
    }
    if (response.data.user) {
      console.log(`   User: ${response.data.user.fullName}`);
    } else {
      console.log(`   Message: ${response.data.message}`);
    }
    
    return response.data;
  } catch (error) {
    console.log(`   ❌ Emotion recognition test failed: ${error.message}`);
    return null;
  }
}

async function testFaceEnrollment(userId = 2) {
  console.log(`\n🔍 5. Testing Face Enrollment (User ID: ${userId})...`);
  
  try {
    const response = await makeRequest('/api/face-enrollment', 'POST', {
      userId: userId,
      imageData: testImageDataURL
    });
    
    console.log(`   Status: ${response.statusCode}`);
    console.log(`   Success: ${response.data.success}`);
    console.log(`   Message: ${response.data.message}`);
    if (response.data.qualityScore) {
      console.log(`   Quality Score: ${response.data.qualityScore}`);
    }
    if (response.data.issues) {
      console.log(`   Issues: ${response.data.issues.join(', ')}`);
    }
    
    return response.data;
  } catch (error) {
    console.log(`   ❌ Face enrollment test failed: ${error.message}`);
    return null;
  }
}

async function testMultipleFaceDetection() {
  console.log('\n🔍 6. Testing Multiple Face Detection...');
  
  try {
    const response = await makeRequest('/api/detect-faces', 'POST', {
      imageData: testImageDataURL
    });
    
    console.log(`   Status: ${response.statusCode}`);
    console.log(`   Face Count: ${response.data.faceCount}`);
    if (response.data.faces && response.data.faces.length > 0) {
      response.data.faces.forEach((face, index) => {
        console.log(`   Face ${index + 1}: Confidence ${face.confidence.toFixed(3)}, Box: ${face.boundingBox.width}x${face.boundingBox.height}`);
      });
    }
    
    return response.data;
  } catch (error) {
    console.log(`   ❌ Multiple face detection test failed: ${error.message}`);
    return null;
  }
}

// Run comprehensive tests
async function runComprehensiveTests() {
  console.log('🚀 Starting Comprehensive Face Recognition System Tests...\n');
  
  const results = {};
  
  // Test 1: Basic API
  results.basicAPI = await testBasicAPI();
  
  // Test 2: Image Quality
  results.imageQuality = await testImageQuality();
  
  // Test 3: Face Recognition (before enrollment)
  results.faceRecognitionBefore = await testFaceRecognition();
  
  // Test 4: Emotion Recognition (before enrollment)
  results.emotionRecognitionBefore = await testEmotionRecognition();
  
  // Test 5: Multiple Face Detection
  results.multipleFaceDetection = await testMultipleFaceDetection();
  
  // Test 6: Face Enrollment
  results.faceEnrollment = await testFaceEnrollment();
  
  // Wait a moment for enrollment to process
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  // Test 7: Face Recognition (after enrollment)
  console.log('\n🔍 7. Testing Face Recognition (After Enrollment)...');
  results.faceRecognitionAfter = await testFaceRecognition();
  
  // Test 8: Emotion Recognition (after enrollment)
  console.log('\n🔍 8. Testing Emotion Recognition (After Enrollment)...');
  results.emotionRecognitionAfter = await testEmotionRecognition();
  
  // Print summary
  console.log('\n📋 TEST SUMMARY');
  console.log('================');
  console.log(`✅ Basic API: ${results.basicAPI ? 'PASS' : 'FAIL'}`);
  console.log(`✅ Image Quality: ${results.imageQuality?.isValid !== undefined ? 'PASS' : 'FAIL'}`);
  console.log(`✅ Face Detection: ${results.multipleFaceDetection?.faceCount >= 0 ? 'PASS' : 'FAIL'}`);
  console.log(`✅ Face Enrollment: ${results.faceEnrollment?.success ? 'PASS' : 'FAIL'}`);
  console.log(`✅ Face Recognition: ${results.faceRecognitionAfter?.recognized ? 'PASS' : 'FAIL'}`);
  console.log(`✅ Emotion Recognition: ${results.emotionRecognitionAfter?.emotion ? 'PASS' : 'FAIL'}`);
  
  return results;
}

// Run the tests
runComprehensiveTests().catch(console.error);
