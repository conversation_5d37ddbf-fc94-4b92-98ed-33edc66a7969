import { useState } from "react";
import { useAuth } from "@/hooks/use-auth";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useToast } from "@/hooks/use-toast";
import { useLocation } from "wouter";
import { Camera, Lock, User, Scan, Shield } from "lucide-react";

export default function Login() {
  const [credentials, setCredentials] = useState({ username: "", password: "" });
  const [isLoading, setIsLoading] = useState(false);
  const { login, isAdmin } = useAuth();
  const { toast } = useToast();
  const [, setLocation] = useLocation();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      const user = await login(credentials);
      toast({
        title: "Welcome back!",
        description: `Successfully logged in as ${user.fullName}`,
      });

      // Clear form after successful login
      setCredentials({ username: "", password: "" });

      // Redirect based on user role
      if (user.role === "admin") {
        setLocation("/admin");
      } else {
        setLocation("/student");
      }
    } catch (error: any) {
      console.error("Login error:", error);
      let errorMessage = "Please check your username and password";

      if (error.message.includes("401")) {
        errorMessage = "Invalid username or password";
      } else if (error.message.includes("500")) {
        errorMessage = "Server error. Please try again later";
      } else if (error.message.includes("Network")) {
        errorMessage = "Network error. Please check your connection";
      }

      toast({
        title: "Authentication failed",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 flex items-center justify-center p-4">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
      
      <div className="relative w-full max-w-6xl grid lg:grid-cols-2 gap-8 items-center">
        {/* Left Side - Branding */}
        <div className="hidden lg:block space-y-8 px-8">
          <div className="space-y-6">
            <div className="flex items-center space-x-3">
              <div className="bg-primary/10 p-3 rounded-xl">
                <Camera className="h-8 w-8 text-primary" />
              </div>
              <div>
                <h1 className="text-3xl font-bold text-gray-900">AttendanceTracker Pro</h1>
                <p className="text-gray-600">Smart Face Recognition System</p>
              </div>
            </div>
            
            <div className="space-y-4 text-gray-700">
              <p className="text-lg leading-relaxed">
                Advanced attendance management with cutting-edge face recognition technology. 
                Secure, reliable, and effortless employee check-ins.
              </p>
            </div>
          </div>

          {/* Features */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900">Key Features</h3>
            <div className="grid gap-3">
              <div className="flex items-center space-x-3">
                <div className="bg-green-100 p-2 rounded-lg">
                  <Scan className="h-5 w-5 text-green-600" />
                </div>
                <span className="text-gray-700">Real-time Face Recognition</span>
              </div>
              <div className="flex items-center space-x-3">
                <div className="bg-blue-100 p-2 rounded-lg">
                  <Shield className="h-5 w-5 text-blue-600" />
                </div>
                <span className="text-gray-700">Secure Access Control</span>
              </div>
              <div className="flex items-center space-x-3">
                <div className="bg-purple-100 p-2 rounded-lg">
                  <Camera className="h-5 w-5 text-purple-600" />
                </div>
                <span className="text-gray-700">Advanced Analytics</span>
              </div>
            </div>
          </div>
        </div>

        {/* Right Side - Login Form */}
        <div className="flex items-center justify-center">
          <Card className="w-full max-w-md shadow-2xl border-0 bg-white/80 backdrop-blur">
            <CardHeader className="text-center space-y-4 pb-6">
              <div className="flex items-center justify-center">
                <div className="bg-gradient-to-r from-primary to-blue-600 p-3 rounded-xl">
                  <Camera className="h-8 w-8 text-white" />
                </div>
              </div>
              <div className="space-y-2">
                <CardTitle className="text-2xl font-bold text-gray-900">Welcome Back</CardTitle>
                <CardDescription className="text-gray-600">
                  Sign in to access your attendance dashboard
                </CardDescription>
              </div>
            </CardHeader>
            
            <CardContent className="space-y-6">
              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="username" className="text-sm font-medium text-gray-700">Username</Label>
                  <div className="relative">
                    <User className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                    <Input
                      id="username"
                      type="text"
                      placeholder="Enter your username"
                      className="pl-10 h-12 border-gray-200 focus:border-primary focus:ring-primary"
                      value={credentials.username}
                      onChange={(e) => setCredentials({ ...credentials, username: e.target.value })}
                      required
                    />
                  </div>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="password" className="text-sm font-medium text-gray-700">Password</Label>
                  <div className="relative">
                    <Lock className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                    <Input
                      id="password"
                      type="password"
                      placeholder="Enter your password"
                      className="pl-10 h-12 border-gray-200 focus:border-primary focus:ring-primary"
                      value={credentials.password}
                      onChange={(e) => setCredentials({ ...credentials, password: e.target.value })}
                      required
                    />
                  </div>
                </div>
                
                <Button 
                  type="submit" 
                  className="w-full h-12 bg-gradient-to-r from-primary to-blue-600 hover:from-primary/90 hover:to-blue-700 text-white font-medium shadow-lg" 
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <div className="flex items-center space-x-2">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                      <span>Authenticating...</span>
                    </div>
                  ) : (
                    "Sign In"
                  )}
                </Button>
              </form>
              
              <div className="pt-4 border-t border-gray-100">
                <div className="text-center space-y-3">
                  <p className="text-sm font-medium text-gray-700">Demo Accounts</p>
                  <div className="grid gap-2 text-xs">
                    <div className="bg-blue-50 p-3 rounded-lg">
                      <p className="font-medium text-blue-900">Administrator Access</p>
                      <p className="text-blue-700"><strong>Username:</strong> admin</p>
                      <p className="text-blue-700"><strong>Password:</strong> admin123</p>
                    </div>
                    <div className="bg-green-50 p-3 rounded-lg">
                      <p className="font-medium text-green-900">Student Access</p>
                      <p className="text-green-700"><strong>Username:</strong> michael.chen</p>
                      <p className="text-green-700"><strong>Password:</strong> password123</p>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
