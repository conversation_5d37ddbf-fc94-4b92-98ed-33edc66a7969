import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const sourceDir = path.join(__dirname, 'node_modules', '@vladmandic', 'face-api', 'model');
const targetDir = path.join(__dirname, 'models');

// Create target directory if it doesn't exist
if (!fs.existsSync(targetDir)) {
  fs.mkdirSync(targetDir, { recursive: true });
  console.log(`📁 Created models directory: ${targetDir}`);
}

// Copy all model files
try {
  const files = fs.readdirSync(sourceDir);
  
  console.log('📋 Copying face recognition models...');
  
  for (const file of files) {
    const sourcePath = path.join(sourceDir, file);
    const targetPath = path.join(targetDir, file);
    
    fs.copyFileSync(sourcePath, targetPath);
    console.log(`✅ Copied ${file}`);
  }
  
  console.log('🎉 All models copied successfully!');
  console.log(`📂 Models location: ${targetDir}`);
  
  // List copied files
  const copiedFiles = fs.readdirSync(targetDir);
  console.log('\n📋 Available models:');
  copiedFiles.forEach(file => {
    const stats = fs.statSync(path.join(targetDir, file));
    const sizeKB = Math.round(stats.size / 1024);
    console.log(`  - ${file} (${sizeKB} KB)`);
  });
  
} catch (error) {
  console.error('❌ Failed to copy models:', error);
  process.exit(1);
}
